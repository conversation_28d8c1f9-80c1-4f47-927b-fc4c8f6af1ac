import React, { useState, useEffect, useRef } from "react";
import {
  Image,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  FlatList,
  Animated,
  Dimensions,
  TextInput,              // ← ADDED
} from "react-native";
import PropTypes from "prop-types";

import MyText from "./MyText";
import colors from "../assets/colors";
import icons from "../assets/icons";
import commonStyles from "../assets/commonStyles";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

const CustomDropDown = ({
  width = "100%",
  data = [],
  onSelect,
  defaultValue = "Select",
  imageIcon,
  label,
  labelStyle,
  labelIcon,
  height = 0,
  backgroundColor = colors.inputBg,
  radius = 12,
  borderWidth = 0,
  borderColor,
  disabled = false,
  showIcon = true,
  marginBottom = 16,

  /*** NEW PROP ***/
  search = false,           // ← ADDED
  /*** END NEW PROP ***/

  onDropdownToggle,
  showVisibilityToggle = false,
  isVisible = false,
  onVisibilityToggle,
  customStyles = {},
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState("top");
  const [buttonLayout, setButtonLayout] = useState({ y: 0, height: 0 });
  const [searchQuery, setSearchQuery] = useState("");        // ← ADDED
  const dropdownButtonRef = useRef(null);
  const rotateAnim = useRef(new Animated.Value(0)).current;


  // Find the initial selected item based on defaultValue
  useEffect(() => {
    if (!Array.isArray(data) || data.length === 0) return;

    if (typeof defaultValue === "string") {
      const matchedItem = data.find((item) => {
        const name = item?.name?.toLowerCase?.();
        const label = item?.label?.toLowerCase?.();
        const value = defaultValue?.toLowerCase?.();
        return name === value || label === value;
      });

      if (matchedItem) {
        setSelectedItem(matchedItem);
      }
    }
  }, [defaultValue, data]);

  // Handle dropdown animation
  useEffect(() => {
    Animated.timing(rotateAnim, {
      toValue: isOpen ? 1 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [isOpen]);

  const toggleDropdown = () => {
    if (disabled) return;

    const newIsOpen = !isOpen;

    if (!isOpen) {
      // Measure the position of the dropdown button
      dropdownButtonRef.current.measure((x, y, width, height, pageX, pageY) => {
        const spaceBelow = SCREEN_HEIGHT - pageY - height;
        const requiredSpace = Math.min(data.length * 40, 200); // Estimate dropdown height

        // If there's not enough space below, position the dropdown above
        if (spaceBelow < requiredSpace) {
          setDropdownPosition("top");
        } else {
          setDropdownPosition("top");
        }

        setButtonLayout({ y: pageY, height });
      });
    }

    // Notify parent component about dropdown state change
    onDropdownToggle?.(newIsOpen);
    setIsOpen(newIsOpen);
    if (!newIsOpen) {
      // Reset search when closing, so next time it always starts fresh
      setSearchQuery("");                               // ← ADDED
    }
  };

  const handleSelect = (item) => {
    setSelectedItem(item);
    setIsOpen(false);
    onDropdownToggle?.(false);
    onSelect?.(item);
    setSearchQuery("");                                   // ← ADDED: clear search after select
  };

  const getDisplayText = () => {
    if (selectedItem?.name || selectedItem?.label) {
      return selectedItem.name || selectedItem.label;
    }
    if (typeof defaultValue === "object" && defaultValue) {
      return defaultValue.name || defaultValue.label || "Select";
    }
    if (typeof defaultValue === "string") {
      return defaultValue;
    }
    return "Select";
  };

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "90deg"],
  });

  /*** FILTERED DATA based on searchQuery ***/
  const filteredData = search
    ? data.filter((item) => {
        const label = (item.label || item.name || "")
          .toString()
          .toLowerCase();
        return label.includes(searchQuery.toLowerCase());
      })
    : data;
  /*** END FILTERED DATA ***/


  return (
    <View style={{ marginBottom: marginBottom }}>
      {/* Label */}
      {label && (
        <View
          style={[
            {
              flexDirection: "row",
              alignItems: "center",
              position: "relative",
            },
          ]}
        >
          {labelIcon && <Image source={labelIcon} style={styles.labelIcon} />}
          <MyText p medium style={[styles.label, labelStyle]}>
            {label}
          </MyText>
        </View>
      )}

      {/* Main container with relative positioning */}
      <View style={{ position: "relative", zIndex: isOpen ? 999 : 1 }}>
        {/* Dropdown Button */}
        <View style={{ position: "relative" }}>
          <TouchableOpacity
            ref={dropdownButtonRef}
            style={[
              styles.dropdownButton,
              {
                width: width,
                height: height || 48,
                backgroundColor,
                borderRadius: radius,
                borderWidth,
                borderColor,
                opacity: disabled ? 0.7 : 1,
                borderBottomLeftRadius:
                  isOpen && dropdownPosition === "bottom" ? 0 : radius,
                borderBottomRightRadius:
                  isOpen && dropdownPosition === "bottom" ? 0 : radius,
                borderTopLeftRadius:
                  isOpen && dropdownPosition === "top" ? 0 : radius,
                borderTopRightRadius:
                  isOpen && dropdownPosition === "top" ? 0 : radius,
              },
              customStyles,
              disabled && { backgroundColor: colors.chipBg, opacity: 0.4},
            ]}
            onPress={toggleDropdown}
            activeOpacity={0.7}
            disabled={disabled}
          >
            <MyText
              style={[
                styles.dropdownButtonText,
                {
                  textAlign: showIcon ? "left" : "center",
                  color: selectedItem ? colors.black : colors.txtGray,
                  textTransform: "capitalize",
                },
              ]}
            >
              {getDisplayText()}
            </MyText>
            {showIcon && (
              <Animated.Image
                source={icons.rightArrowIcon}
                style={[
                  commonStyles?.extraSmallIcon,
                  { transform: [{ rotate }] },
                ]}
                resizeMode={"contain"}
              />
            )}
          </TouchableOpacity>

          {/* Visibility Toggle */}
          {showVisibilityToggle && (
            <TouchableOpacity
              style={styles.visibilityToggle}
              onPress={onVisibilityToggle}
              activeOpacity={0.7}
              disabled={disabled}
            >
              <Image
                source={
                  isVisible
                    ? icons.openEyeProfileIcon
                    : icons.closedEyeProfileIcon
                }
                style={styles.visibilityIcon}
                resizeMode="contain"
              />
            </TouchableOpacity>
          )}
        </View>

        {/* Dropdown List - Positioned based on available space */}
        {isOpen && (
          <View
            style={[
              styles.dropdown,
              dropdownPosition === "top"
                ? styles.dropdownTop
                : styles.dropdownBottom,
              {
                width: width,
                maxHeight: 200,
                borderTopLeftRadius:
                  dropdownPosition === "bottom" ? 0 : 8,
                borderTopRightRadius:
                  dropdownPosition === "bottom" ? 0 : 8,
                borderBottomLeftRadius:
                  dropdownPosition === "top" ? 0 : 8,
                borderBottomRightRadius:
                  dropdownPosition === "top" ? 0 : 8,
              },
            ]}
          >
            {/* ← ONLY SHOW TEXTINPUT WHEN search === true */}
            {search && (
              <TextInput
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Search..."
                style={styles.searchInput}   // ← ADDED STYLE
                autoFocus
              />
            )}

            <FlatList
              data={filteredData}
              keyExtractor={(_, index) => `dropdown-item-${index}`}
              showsVerticalScrollIndicator={false}
              bounces={false}
              renderItem={({ item }) => {
                const isSelected =
                  (selectedItem?.value &&
                    item?.value &&
                    selectedItem.value === item.value) ||
                  (selectedItem?.label &&
                    item?.label &&
                    selectedItem.label === item.label);

                return (
                  <TouchableOpacity
                    style={[
                      styles.dropdownItem,
                      isSelected && { backgroundColor: colors.primary },
                    ]}
                    onPress={() => handleSelect(item)}
                  >
                    {imageIcon && (
                      <Image
                        source={imageIcon}
                        style={styles.dropdownItemIcon}
                      />
                    )}
                    <Text
                      style={[
                        styles.dropdownItemText,
                        { color: isSelected ? colors.white : colors.black },
                      ]}
                    >
                      {item.label || item.name || "Unknown"}
                    </Text>
                  </TouchableOpacity>
                );
              }}
            />
          </View>
        )}
      </View>
    </View>
  );
};

CustomDropDown.propTypes = {
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  data: PropTypes.array,
  onSelect: PropTypes.func,
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  imageIcon: PropTypes.any,
  label: PropTypes.string,
  labelStyle: PropTypes.object,
  labelIcon: PropTypes.any,
  height: PropTypes.number,
  backgroundColor: PropTypes.string,
  radius: PropTypes.number,
  borderWidth: PropTypes.number,
  borderColor: PropTypes.string,
  disabled: PropTypes.bool,
  showIcon: PropTypes.bool,
  marginVertical: PropTypes.number,
  onDropdownToggle: PropTypes.func,
  showVisibilityToggle: PropTypes.bool,
  isVisible: PropTypes.bool,
  onVisibilityToggle: PropTypes.func,

  /*** ADD search PROP TYPE ***/
  search: PropTypes.bool,   // ← ADDED
};

const styles = StyleSheet.create({
  dropdownButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 12,
    marginTop: 6,
  },
  dropdownButtonText: {
    flex: 1,
    fontSize: 14,
    fontFamily: "Metropolis-Regular",
  },
  dropdownIcon: {
    width: 20,
    height: 20,
    resizeMode: "contain",
  },
  labelIcon: {
    width: 16,
    height: 16,
    marginRight: 8,
    resizeMode: "contain",
  },
  dropdown: {
    position: "absolute",
    left: 0,
    right: 0,
    backgroundColor: colors.inputBg,
    borderRadius: 8,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 1000,
  },
  dropdownTop: {
    bottom: "100%",
    marginBottom: 0,
  },
  dropdownBottom: {
    top: "100%",
    marginTop: 0,
  },
  dropdownItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  dropdownItemText: {
    flex: 1,
    fontSize: 14,
    fontFamily: "Metropolis-Regular",
  },
  dropdownItemIcon: {
    width: 20,
    height: 20,
    marginRight: 8,
  },
  visibilityToggle: {
    position: "absolute",
    right: 12,
    top: "50%",
    transform: [{ translateY: -10 }],
    padding: 5,
    zIndex: 1,
  },
  visibilityIcon: {
    width: 20,
    height: 20,
  },
  /*** NEW STYLE FOR SEARCH INPUT ***/
  searchInput: {
    height: 40,
    borderBottomWidth: 1,
    borderColor: colors.gray3,
    paddingHorizontal: 10,
    marginHorizontal: 12,
    marginTop: 8,
    marginBottom: 4,
    borderRadius: 4,
    fontSize: 14,
    fontFamily: "Metropolis-Regular",
  },
});

export default CustomDropDown;
