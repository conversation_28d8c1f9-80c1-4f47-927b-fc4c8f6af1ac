import React, { useEffect } from 'react';
import {
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import icons from '../assets/icons';
import colors from '../assets/colors';
import { useDispatch } from 'react-redux';
import { GenerateImageLink } from '../redux/features/mainSlice';
import FastImage from '@d11/react-native-fast-image';
import images from '../assets/images';

const PhotoUploader = ({ uri, onUploadSuccess, customStyle, notEditable }) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = React.useState(false);
  const [imageUri, setImageUri] = React.useState(uri);

useEffect(() => {
    if (uri) {
        setImageUri(uri);
    }
    }, [uri]);


  const handleImageSelect = () => {
    Alert.alert(
      'Update Profile Picture',
      'Choose a method',
      [
        { text: 'Camera', onPress: openCamera },
        { text: 'Gallery', onPress: openGallery },
        { text: 'Cancel', style: 'cancel' },
      ],
      { cancelable: true }
    );
  };

  const openCamera = async () => {
    try {
      const image = await ImagePicker.openCamera({
        cropping: true,
        compressImageQuality: 0.8,
      });
      await uploadImage(image);
    } catch (error) {
      console.log('Camera cancelled or failed:', error?.message);
    }
  };

  const openGallery = async () => {
    try {
      const image = await ImagePicker.openPicker({
        cropping: true,
        compressImageQuality: 0.8,
      });
      await uploadImage(image);
    } catch (error) {
      console.log('Gallery cancelled or failed:', error?.message);
    }
  };

  const uploadImage = async (image) => {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('profile_image', {
        uri: image.path,
        name: 'profile.jpg',
        type: image.mime,
      });

      const response = await dispatch(GenerateImageLink(formData));
      console.log('Upload response:', JSON.stringify(response));

      if(response?.payload?.success) {
        onUploadSuccess(response?.payload?.data?.profile_image?.s3Url);
        setImageUri(response?.payload?.data?.profile_image?.s3Url);
      } else {
        Alert.alert('Upload Failed', 'Something went wrong while uploading.');
      }

    } catch (err) {
      console.error('Upload error:', err);
      Alert.alert('Upload Failed', 'Something went wrong while uploading.');
    } finally {
      setLoading(false);
    }

  };

  // console.log('Profile Picture URI:', uri);
  

  return (
    <TouchableOpacity onPress={handleImageSelect} style={styles.wrapper}>
      {loading ? <ActivityIndicator size="large" color={colors.primary} /> :
        <FastImage source={imageUri ? {uri: imageUri, priority: FastImage.priority.high } : images.companyLogo} 
        style={[styles.image, customStyle]} />}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    alignSelf: 'center',
    // marginVertical: 20,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 12,
    backgroundColor: '#ccc',
  },
  editIconWrapper: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 8,
  },
  icon: {
    width: 16,
    height: 16,
    tintColor: colors.primary,
  },
});

export default PhotoUploader;
