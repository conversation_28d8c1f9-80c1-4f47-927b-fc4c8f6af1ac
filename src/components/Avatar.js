import React from "react";
import {
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import MyText from "./MyText";
import colors from "../assets/colors";
import commonStyles from "../assets/commonStyles";
import FastImage from "@d11/react-native-fast-image";

const Avatar = (props) => {
  const { url, name = "Alice", container, size, microsoft } = props;
  const isBase64 =
    url?.startsWith("data:application/") ||
    url?.startsWith("/var/mobile/Containers/Data/Application");

  // console.log("🚀 ~ Avatar ~ url:", url, isBase64);

  const getInitials = () => {
    const [firstName, lastName] = name.split(" ");

    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    }

    return firstName.charAt(0).toUpperCase();
  };

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={props.onPress}
      style={[
        styles.container,
        container,
        commonStyles?.header?.midIcon,
        commonStyles?.shadow,
        { width: size, height: size },
      ]}
    >
      {url ? (
        isBase64 ? (
          <Image
            source={{ uri: url }}
            style={{ width: size, height: size, borderRadius: size / 2 }}
            resizeMode="cover"
          />
        ) : (
          <FastImage
            source={{ uri: url, priority: FastImage.priority.high }}
            style={{ width: size, height: size, borderRadius: size / 2 }}
            resizeMode={FastImage.resizeMode.cover}
          />
        )
      ) : (
        <MyText bold>{getInitials()}</MyText>
      )}
    </TouchableOpacity>
  );
};

export default Avatar;

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors?.white,
    borderRadius: 30,
  },
});
