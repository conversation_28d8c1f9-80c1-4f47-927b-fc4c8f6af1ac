import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { check, RESULTS } from "react-native-permissions";
import Contacts from "react-native-contacts";
// import Contacts from "react-native-contacts/src/NativeContacts";
import { getPlatformSpecificContactsPermission } from "./permissionHelper";
import { fetchMicrosoftContacts, signInAndGetToken } from "./commonHelpers";

/**
 * Fetches contacts from different sources (local device, Google, Microsoft)
 *
 * @param {string} source - The source to fetch contacts from ('local_device', 'google', 'microsoft')
 * @param {function} onSuccess - Callback function to be called on successful fetch with contacts data
 * @param {function} onError - Callback function to be called on error
 * @param {function} setLoading - Function to set loading state
 * @returns {Promise<void>}
 */
export const fetchContactsFromSource = async (
  source,
  onSuccess,
  onError = (error) => console.error("Error fetching contacts:", error),
  setLoading = () => {}
) => {
  setLoading(true);

  try {
    let contactsData = [];

    if (source === "local_device") {
      const permission = getPlatformSpecificContactsPermission();
      const status = await check(permission);

      if (status === RESULTS.GRANTED) {
        contactsData = await Contacts.getAll();
        if (contactsData.length > 0) {
          onSuccess(contactsData, source);
        } else {
          onError(new Error("No contacts found on device"));
        }
      } else {
        onError(new Error("Permission not granted to access contacts"));
      }
    } else if (source === "google") {
      await GoogleSignin.signIn();
      const tokens = await GoogleSignin.getTokens();
      let allContacts = [];
      let nextPageToken = undefined;
      do {
        const url =
          "https://people.googleapis.com/v1/people/me/connections?personFields=names,emailAddresses,phoneNumbers,photos&pageSize=1000" +
          (nextPageToken ? `&pageToken=${nextPageToken}` : "");
        const response = await fetch(url, {
          method: "GET",
          headers: { Authorization: `Bearer ${tokens.accessToken}` },
        });
        const data = await response.json();
        if (data.connections) {
          allContacts = allContacts.concat(data.connections);
        }
        nextPageToken = data.nextPageToken;
      } while (nextPageToken);
      if (allContacts.length > 0) {
        onSuccess(allContacts, source);
      } else {
        onError(
          new Error("No contacts found or an error occurred with Google")
        );
      }
    } else if (source === "microsoft") {
      try {
        const token = await signInAndGetToken();
        contactsData = await fetchMicrosoftContacts(token, true); // Include photos
        onSuccess(contactsData, source);
      } catch (err) {
        onError(new Error("Error with Microsoft contacts: " + err.message));
      }
    } else if (source === "iCloud") {
      // Placeholder for iCloud implementation
      onError(new Error("iCloud import not implemented yet"));
    }
  } catch (error) {
    onError(error);
  } finally {
    setLoading(false);
  }
};
