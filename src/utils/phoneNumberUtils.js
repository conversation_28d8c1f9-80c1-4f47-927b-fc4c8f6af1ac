import {
  parsePhoneNumber,
  isValidPhoneNumber,
  AsYouType,
} from "libphonenumber-js";

/**
 * Default country code to use when no country code is detected
 */
const DEFAULT_COUNTRY_CODE = "+91"; // India
const DEFAULT_COUNTRY = "IN"; // India ISO code

/**
 * Processes and validates phone numbers, adding country code if missing
 * @param {Array} phoneNumbers - Array of phone number objects
 * @param {string} defaultCountryCode - Default country code to add (default: +91)
 * @param {string} defaultCountry - Default country ISO code (default: IN)
 * @returns {Array} Processed phone numbers with proper formatting
 */
export const processPhoneNumbersWithCountryCode = (
  phoneNumbers = [],
  defaultCountryCode = DEFAULT_COUNTRY_CODE,
  defaultCountry = DEFAULT_COUNTRY
) => {
  if (!Array.isArray(phoneNumbers)) {
    console.warn(
      "processPhoneNumbersWithCountryCode: phoneNumbers is not an array"
    );
    return [];
  }

  return phoneNumbers.map((phoneObj, index) => {
    try {
      let phoneNumber = phoneObj.number || phoneObj.phone || "";

      // Skip if no phone number
      if (!phoneNumber || typeof phoneNumber !== "string") {
        console.warn(
          `Phone number at index ${index} is empty or invalid:`,
          phoneObj
        );
        return {
          ...phoneObj,
          number: "",
          countryCode: "",
          isValid: false,
          originalNumber: phoneNumber,
        };
      }

      // Clean the phone number (remove spaces, dashes, etc.)
      const cleanedNumber = phoneNumber.replace(/[\s\-\(\)\.]/g, "");

      // Check if number already has a country code (starts with +)
      const hasCountryCode = cleanedNumber.startsWith("+");

      let processedNumber = cleanedNumber;
      let detectedCountryCode = "";
      let isValid = false;
      let formattedNumber = "";

      if (hasCountryCode) {
        // Number already has country code, validate it
        try {
          const parsed = parsePhoneNumber(cleanedNumber);
          if (parsed) {
            detectedCountryCode = `+${parsed.countryCallingCode}`;
            formattedNumber = parsed.formatInternational();
            isValid = parsed.isValid();
            processedNumber = parsed.nationalNumber;
          }
        } catch (error) {
          console.warn(
            `Error parsing phone number with country code: ${cleanedNumber}`,
            error
          );
          // Fallback: keep original number
          processedNumber = cleanedNumber;
          isValid = false;
        }
      } else {
        // Number doesn't have country code, add default
        const numberWithCountryCode = `${defaultCountryCode}${cleanedNumber}`;

        try {
          const parsed = parsePhoneNumber(
            numberWithCountryCode,
            defaultCountry
          );
          if (parsed) {
            detectedCountryCode = defaultCountryCode;
            formattedNumber = parsed.formatInternational();
            isValid = parsed.isValid();
            processedNumber = parsed.nationalNumber;
          } else {
            // Fallback validation
            isValid = isValidPhoneNumber(numberWithCountryCode, defaultCountry);
            detectedCountryCode = defaultCountryCode;
            processedNumber = cleanedNumber;
            formattedNumber = numberWithCountryCode;
          }
        } catch (error) {
          console.warn(
            `Error parsing phone number: ${numberWithCountryCode}`,
            error
          );
          // Fallback: add country code but mark as potentially invalid
          detectedCountryCode = defaultCountryCode;
          processedNumber = cleanedNumber;
          formattedNumber = numberWithCountryCode;
          isValid = false;
        }
      }

      return {
        ...phoneObj,
        number: processedNumber,
        countryCode: detectedCountryCode,
        formattedNumber: formattedNumber,
        isValid: isValid,
        originalNumber: phoneNumber,
        type: phoneObj.type || phoneObj.label || "mobile",
      };
    } catch (error) {
      console.error(`Error processing phone number at index ${index}:`, error);
      return {
        ...phoneObj,
        number: phoneObj.number || "",
        countryCode: "",
        isValid: false,
        originalNumber: phoneObj.number || "",
      };
    }
  });
};

/**
 * Validates a single phone number
 * @param {string} phoneNumber - Phone number to validate
 * @param {string} country - Country code (ISO format)
 * @returns {Object} Validation result
 */
export const validatePhoneNumber = (phoneNumber, country = DEFAULT_COUNTRY) => {
  try {
    if (!phoneNumber || typeof phoneNumber !== "string") {
      return { isValid: false, error: "Invalid phone number input" };
    }

    const cleanedNumber = phoneNumber.replace(/[\s\-\(\)\.]/g, "");

    if (cleanedNumber.startsWith("+")) {
      // International format
      const parsed = parsePhoneNumber(cleanedNumber);
      return {
        isValid: parsed ? parsed.isValid() : false,
        formatted: parsed ? parsed.formatInternational() : cleanedNumber,
        countryCode: parsed ? `+${parsed.countryCallingCode}` : "",
        nationalNumber: parsed ? parsed.nationalNumber : "",
      };
    } else {
      // National format, add default country
      const withCountryCode = `${DEFAULT_COUNTRY_CODE}${cleanedNumber}`;
      const parsed = parsePhoneNumber(withCountryCode, country);
      return {
        isValid: parsed ? parsed.isValid() : false,
        formatted: parsed ? parsed.formatInternational() : withCountryCode,
        countryCode: DEFAULT_COUNTRY_CODE,
        nationalNumber: parsed ? parsed.nationalNumber : cleanedNumber,
      };
    }
  } catch (error) {
    return { isValid: false, error: error.message };
  }
};

/**
 * Formats phone number as you type
 * @param {string} phoneNumber - Phone number being typed
 * @param {string} country - Country code (ISO format)
 * @returns {string} Formatted phone number
 */
export const formatAsYouType = (phoneNumber, country = DEFAULT_COUNTRY) => {
  try {
    const formatter = new AsYouType(country);
    return formatter.input(phoneNumber);
  } catch (error) {
    console.warn("Error formatting phone number as you type:", error);
    return phoneNumber;
  }
};

/**
 * Extracts country code from a phone number
 * @param {string} phoneNumber - Phone number with country code
 * @returns {string} Country code (e.g., '+91')
 */
export const extractCountryCode = (phoneNumber) => {
  try {
    if (!phoneNumber || typeof phoneNumber !== "string") {
      return "";
    }

    const cleanedNumber = phoneNumber.replace(/[\s\-\(\)\.]/g, "");

    if (cleanedNumber.startsWith("+")) {
      const parsed = parsePhoneNumber(cleanedNumber);
      return parsed ? `+${parsed.countryCallingCode}` : "";
    }

    return "";
  } catch (error) {
    console.warn("Error extracting country code:", error);
    return "";
  }
};

/**
 * Processes a single phone number string and adds country code if missing
 * @param {string} phoneNumber - Phone number string
 * @param {string} defaultCountryCode - Default country code to add (default: +91)
 * @param {string} defaultCountry - Default country ISO code (default: IN)
 * @returns {Object} Processed phone number object
 */
export const processSinglePhoneNumber = (
  phoneNumber,
  defaultCountryCode = DEFAULT_COUNTRY_CODE,
  defaultCountry = DEFAULT_COUNTRY
) => {
  if (!phoneNumber || typeof phoneNumber !== "string") {
    return {
      number: "",
      countryCode: "",
      formattedNumber: "",
      isValid: false,
      originalNumber: phoneNumber || "",
    };
  }

  // Clean the phone number
  const cleanedNumber = phoneNumber.replace(/[\s\-\(\)\.]/g, "");

  // Check if number already has a country code
  const hasCountryCode = cleanedNumber.startsWith("+");

  try {
    if (hasCountryCode) {
      // Number already has country code
      const parsed = parsePhoneNumber(cleanedNumber);
      if (parsed) {
        return {
          number: parsed.nationalNumber,
          countryCode: `+${parsed.countryCallingCode}`,
          formattedNumber: parsed.formatInternational(),
          isValid: parsed.isValid(),
          originalNumber: phoneNumber,
        };
      }
    } else {
      // Add default country code
      const numberWithCountryCode = `${defaultCountryCode}${cleanedNumber}`;
      const parsed = parsePhoneNumber(numberWithCountryCode, defaultCountry);
      if (parsed) {
        return {
          number: parsed.nationalNumber,
          countryCode: defaultCountryCode,
          formattedNumber: parsed.formatInternational(),
          isValid: parsed.isValid(),
          originalNumber: phoneNumber,
        };
      }
    }
  } catch (error) {
    console.warn("Error processing phone number:", error);
  }

  // Fallback
  return {
    number: cleanedNumber,
    countryCode: hasCountryCode ? "" : defaultCountryCode,
    formattedNumber: hasCountryCode
      ? cleanedNumber
      : `${defaultCountryCode}${cleanedNumber}`,
    isValid: false,
    originalNumber: phoneNumber,
  };
};

/**
 * Utility function to process existing contact data and add country codes where missing
 * This can be used to update existing contacts in the system
 * @param {Array} contacts - Array of contact objects
 * @param {string} defaultCountryCode - Default country code to add (default: +91)
 * @param {string} defaultCountry - Default country ISO code (default: IN)
 * @returns {Array} Updated contacts with processed phone numbers
 */
export const processExistingContactsPhoneNumbers = (
  contacts = [],
  defaultCountryCode = DEFAULT_COUNTRY_CODE,
  defaultCountry = DEFAULT_COUNTRY
) => {
  if (!Array.isArray(contacts)) {
    console.warn(
      "processExistingContactsPhoneNumbers: contacts is not an array"
    );
    return [];
  }

  return contacts.map((contact) => {
    if (
      !contact ||
      !contact.phoneNumbers ||
      !Array.isArray(contact.phoneNumbers)
    ) {
      return contact;
    }

    const processedPhoneNumbers = processPhoneNumbersWithCountryCode(
      contact.phoneNumbers,
      defaultCountryCode,
      defaultCountry
    );

    return {
      ...contact,
      phoneNumbers: processedPhoneNumbers,
    };
  });
};

/**
 * Check if a phone number needs country code processing
 * @param {string} phoneNumber - Phone number to check
 * @returns {boolean} True if phone number needs country code
 */
export const needsCountryCodeProcessing = (phoneNumber) => {
  if (!phoneNumber || typeof phoneNumber !== "string") {
    return false;
  }

  const cleanedNumber = phoneNumber.replace(/[\s\-\(\)\.]/g, "");
  return !cleanedNumber.startsWith("+");
};
