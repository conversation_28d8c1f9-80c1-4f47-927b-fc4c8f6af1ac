/**
 * Demo file showing how the phone number processing works
 * This file demonstrates the new phone number validation and country code addition functionality
 */

import {
  processPhoneNumbersWithCountryCode,
  processSinglePhoneNumber,
  validatePhoneNumber,
  extractCountryCode,
  needsCountryCodeProcessing,
} from './phoneNumberUtils';

// Example: Processing phone numbers from local contacts
export const demoLocalContactProcessing = () => {
  console.log('=== Demo: Local Contact Phone Number Processing ===');
  
  // Simulate raw phone numbers from local device contacts
  const rawPhoneNumbers = [
    { number: '9876543210', type: 'mobile' },           // No country code
    { number: '8765432109', type: 'home' },             // No country code
    { number: '+************', type: 'work' },          // Already has +91
    { number: '+1234567890', type: 'international' },   // Different country code
    { number: '98765-43210', type: 'mobile' },          // With formatting
    { number: '(*************', type: 'home' },         // US style formatting
    { number: '', type: 'empty' },                      // Empty number
  ];

  console.log('Raw phone numbers:', rawPhoneNumbers);
  
  const processedNumbers = processPhoneNumbersWithCountryCode(rawPhoneNumbers);
  
  console.log('Processed phone numbers:');
  processedNumbers.forEach((phone, index) => {
    console.log(`${index + 1}. Original: "${phone.originalNumber}" -> Processed: "${phone.formattedNumber}" (${phone.countryCode}) - Valid: ${phone.isValid}`);
  });
  
  return processedNumbers;
};

// Example: Processing individual phone numbers
export const demoSingleNumberProcessing = () => {
  console.log('\n=== Demo: Single Phone Number Processing ===');
  
  const testNumbers = [
    '9876543210',        // Indian mobile without country code
    '+************',     // Indian mobile with country code
    '+1234567890',       // US number
    '98765-43210',       // With dashes
    '(*************',    // With parentheses
    '',                  // Empty
    'invalid',           // Invalid
  ];

  testNumbers.forEach(number => {
    const result = processSinglePhoneNumber(number);
    console.log(`"${number}" -> ${result.formattedNumber} (Valid: ${result.isValid})`);
  });
};

// Example: Validation demo
export const demoValidation = () => {
  console.log('\n=== Demo: Phone Number Validation ===');
  
  const testNumbers = [
    '9876543210',
    '+************',
    '+1234567890',
    '123',
    'invalid',
  ];

  testNumbers.forEach(number => {
    const result = validatePhoneNumber(number);
    console.log(`"${number}" -> Valid: ${result.isValid}, Formatted: ${result.formatted}`);
  });
};

// Example: Country code extraction
export const demoCountryCodeExtraction = () => {
  console.log('\n=== Demo: Country Code Extraction ===');
  
  const testNumbers = [
    '+************',
    '+1234567890',
    '+44123456789',
    '9876543210',
    '',
  ];

  testNumbers.forEach(number => {
    const countryCode = extractCountryCode(number);
    console.log(`"${number}" -> Country Code: "${countryCode}"`);
  });
};

// Example: Check if number needs processing
export const demoNeedsProcessing = () => {
  console.log('\n=== Demo: Check if Number Needs Country Code Processing ===');
  
  const testNumbers = [
    '9876543210',        // Needs +91
    '+************',     // Already has country code
    '+1234567890',       // Already has country code
    '',                  // Empty
  ];

  testNumbers.forEach(number => {
    const needs = needsCountryCodeProcessing(number);
    console.log(`"${number}" -> Needs processing: ${needs}`);
  });
};

// Run all demos
export const runAllDemos = () => {
  console.log('🚀 Running Phone Number Processing Demos...\n');
  
  demoLocalContactProcessing();
  demoSingleNumberProcessing();
  demoValidation();
  demoCountryCodeExtraction();
  demoNeedsProcessing();
  
  console.log('\n✅ All demos completed!');
};

// Example of how this integrates with the existing formatContact function
export const demoContactFormatting = () => {
  console.log('\n=== Demo: Contact Formatting Integration ===');
  
  // Simulate a local device contact
  const localContact = {
    recordID: '12345',
    givenName: 'John',
    familyName: 'Doe',
    phoneNumbers: [
      { number: '9876543210', label: 'mobile' },
      { number: '+************', label: 'work' },
      { number: '8765432109', label: 'home' },
    ],
    emailAddresses: [
      { email: '<EMAIL>', label: 'work' }
    ]
  };

  console.log('Original contact:', localContact);
  
  // This is what happens in formatContact function for local_device source
  const rawPhoneNumbers = localContact.phoneNumbers.map(phone => ({
    number: phone.number || '',
    countryCode: phone.countryCode || '',
    type: phone.label || '',
  }));

  const processedPhoneNumbers = processPhoneNumbersWithCountryCode(rawPhoneNumbers);
  
  console.log('Processed phone numbers for contact:');
  processedPhoneNumbers.forEach(phone => {
    console.log(`- ${phone.type}: ${phone.formattedNumber} (Valid: ${phone.isValid})`);
  });
  
  return {
    ...localContact,
    phoneNumbers: processedPhoneNumbers,
  };
};

// Uncomment the line below to run demos when this file is imported
// runAllDemos();
