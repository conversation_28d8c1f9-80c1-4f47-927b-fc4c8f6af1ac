# Phone Number Processing with Country Code Validation

This document describes the new phone number processing functionality that automatically adds country codes to phone numbers imported from local contacts.

## Overview

When importing contacts from the local device, the system now automatically:
- Validates phone numbers using the `libphonenumber-js` library
- Adds `+91` (India) country code to numbers that don't have a country code
- Preserves existing country codes for international numbers
- Maintains backward compatibility with existing functionality

## Files Modified/Added

### New Files
- `src/utils/phoneNumberUtils.js` - Core phone number processing utilities
- `src/utils/__tests__/phoneNumberUtils.test.js` - Unit tests
- `src/utils/phoneNumberDemo.js` - Demo and examples

### Modified Files
- `src/utils/commonHelpers.js` - Updated `formatContact` function for local contacts
- `package.json` - Added `libphonenumber-js` dependency

## Key Functions

### `processPhoneNumbersWithCountryCode(phoneNumbers, defaultCountryCode, defaultCountry)`
Processes an array of phone number objects and adds country codes where missing.

**Parameters:**
- `phoneNumbers` (Array): Array of phone number objects
- `defaultCountryCode` (String): Default country code (default: '+91')
- `defaultCountry` (String): Default country ISO code (default: 'IN')

**Returns:** Array of processed phone number objects with additional fields:
- `countryCode`: The country code (e.g., '+91')
- `formattedNumber`: Internationally formatted number
- `isValid`: Boolean indicating if the number is valid
- `originalNumber`: The original input number

### `processSinglePhoneNumber(phoneNumber, defaultCountryCode, defaultCountry)`
Processes a single phone number string.

### `validatePhoneNumber(phoneNumber, country)`
Validates a phone number and returns validation details.

### `extractCountryCode(phoneNumber)`
Extracts the country code from a phone number.

### `needsCountryCodeProcessing(phoneNumber)`
Checks if a phone number needs country code processing.

## Usage Examples

### Basic Usage
```javascript
import { processPhoneNumbersWithCountryCode } from './phoneNumberUtils';

const phoneNumbers = [
  { number: '9876543210', type: 'mobile' },
  { number: '+************', type: 'work' },
];

const processed = processPhoneNumbersWithCountryCode(phoneNumbers);
// Result:
// [
//   { number: '9876543210', countryCode: '+91', formattedNumber: '+91 98765 43210', isValid: true, ... },
//   { number: '9876543210', countryCode: '+91', formattedNumber: '+91 98765 43210', isValid: true, ... }
// ]
```

### Integration with Contact Import
The functionality is automatically applied when importing local contacts through the `ImportContactScreen`. The `formatContact` function in `commonHelpers.js` now processes phone numbers for local contacts.

## Behavior

### Numbers WITHOUT Country Code
- `9876543210` → `+91 98765 43210`
- `8765432109` → `+91 87654 32109`

### Numbers WITH Country Code (Preserved)
- `+************` → `+91 98765 43210`
- `+1234567890` → `****** 567 890`

### Invalid/Empty Numbers
- Empty strings remain empty
- Invalid numbers are marked as `isValid: false`
- Original numbers are preserved in `originalNumber` field

## Configuration

The default country code is set to `+91` (India) but can be customized by passing different parameters to the processing functions.

```javascript
// Use different default country
const processed = processPhoneNumbersWithCountryCode(
  phoneNumbers, 
  '+1',  // US country code
  'US'   // US country ISO code
);
```

## Testing

Run the tests with:
```bash
npm test -- --testPathPattern=phoneNumberUtils.test.js
```

## Demo

To see the functionality in action, you can run the demo:
```javascript
import { runAllDemos } from './phoneNumberDemo';
runAllDemos();
```

## Backward Compatibility

- Existing functionality for Google and Microsoft contacts remains unchanged
- Only local device contacts are processed with the new functionality
- All existing contact data structures are preserved
- Additional fields are added without removing existing ones

## Dependencies

- `libphonenumber-js`: For phone number parsing, validation, and formatting

## Notes

- The processing includes debug logging that can be removed in production
- Phone numbers are cleaned of formatting characters before processing
- The system handles various input formats (with spaces, dashes, parentheses)
- Invalid numbers are handled gracefully without breaking the import process
