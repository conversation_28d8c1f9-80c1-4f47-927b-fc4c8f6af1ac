import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  KeyboardAvoidingView,
} from "react-native";
import BackButton from "../../components/BackButton";
import InputField from "../../components/InputField";
import { useDispatch } from "react-redux";
import {
  setToken,
  setUser,
  verifyForgetPasswordOtp,
  verifyLoginOtp,
  verifySignupOtp,
  verifySocialSignupOtp,
} from "../../redux/features/authSlice";
import { CommonActions } from "@react-navigation/native";

const TwoFactorScreen = ({ navigation, route }) => {
  const {
    heading = "Verification",
    subheading = "An OTP has been sent on your number and mail ID",
    showEmailVerification = true,
    showPhoneVerification = true,
    showProgressLine = false,
  } = route.params || {};

  const [emailTimer, setEmailTimer] = useState(30);
  const [phoneTimer, setPhoneTimer] = useState(30);
  const [emailOtp, setEmailOtp] = useState("");
  const [phoneOtp, setPhoneOtp] = useState("");
  const [emailError, setEmailError] = useState("");
  const [phoneError, setPhoneError] = useState("");

  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const emailCountdown =
      emailTimer > 0 &&
      setInterval(() => setEmailTimer((prev) => prev - 1), 1000);
    return () => clearInterval(emailCountdown);
  }, [emailTimer]);

  useEffect(() => {
    const phoneCountdown =
      phoneTimer > 0 &&
      setInterval(() => setPhoneTimer((prev) => prev - 1), 1000);
    return () => clearInterval(phoneCountdown);
  }, [phoneTimer]);

  const formatTime = (val) => `00 : ${val < 10 ? "0" + val : val}`;

  const handleSubmitForSignUp = async () => {
    if (!emailOtp) {
      setEmailError("Please enter the OTP sent to your email");
    }
    if (/[a-zA-Z]/.test(emailOtp)) {
      setEmailError("OTP should only contain numbers");
      return;
    }
    if (!phoneOtp) {
      setPhoneError("Please enter the OTP sent to your phone");
    }
    if (/[a-zA-Z]/.test(phoneOtp)) {
      setPhoneError("OTP should only contain numbers");
      return;
    }

    if (emailOtp.length < 4) {
      setEmailError("Please enter a valid OTP");
      return;
    }
    if (phoneOtp.length < 4) {
      setPhoneError("Please enter a valid OTP");
      return;
    }

    const payload = {
      userId: route.params.userID,
      emailOtp: emailOtp,
      phoneOtp: phoneOtp,
    };

    try {
      setLoading(true);
      const response = await dispatch(verifySignupOtp(payload));
      console.log("Verification Response:", JSON.stringify(response));

      if (response?.payload?.success) {
        console.log("Verification successful");
        dispatch(setUser(response?.payload?.data.user));
        dispatch(setToken(response?.payload?.data.accessToken));
        // navigation.navigate("MainApp");
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "MainApp" }],
          })
        );
      } else {
        console.error("Verification failed:", response?.payload?.data.message);
        // setEmailError(response?.payload?.data.message);
        // setPhoneError(response?.payload?.data.message);
        if(response?.payload?.data?.message?.includes("email") && response?.payload?.data?.message?.includes("phone")) {
          setEmailError("Please enter correct OTP sent to your email");
          setPhoneError("Please enter correct OTP sent to your phone");
        } else if(response?.payload?.data?.message?.includes("phone")) {
          setPhoneError(response?.payload?.data?.message);
        } else if(response?.payload?.data?.message?.includes("email")) {
          setEmailError(response?.payload?.data?.message);
        } else {
          setEmailError("Verification failed. Please try again.");
          setPhoneError("Verification failed. Please try again.");
        }
      }
    } catch (error) {
      console.error("Verification Error:", error);
      setEmailError("Verification failed. Please try again.");
      setPhoneError("Verification failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };
  const handleSubmitForSocialSignup = async () => {
    if (!phoneOtp) {
      setPhoneError("Please enter the OTP sent to your phone");
    }
    if (/[a-zA-Z]/.test(phoneOtp)) {
      setPhoneError("OTP should only contain numbers");
      return;
    }

    if (phoneOtp.length < 4) {
      setPhoneError("Please enter a valid OTP");
      return;
    }

    const payload = {
      userId: route.params.userID,
      phoneOtp: phoneOtp,
    };

    try {
      setLoading(true);
      const response = await dispatch(verifySocialSignupOtp(payload));
      console.log("Verification Response:", JSON.stringify(response));

      if (response?.payload?.success) {
        console.log("Verification successful");
        await dispatch(setUser(response?.payload?.data?.user));
        await dispatch(setToken(response?.payload?.data?.accessToken));

        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "MainApp" }],
          })
        );
      } else {
        console.error("Verification failed:", response?.payload?.data?.message);
        setPhoneError(response?.payload?.data?.message);
      }
    } catch (error) {
      console.error("Verification Error:", error);
      setPhoneError("Verification failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };
  const handleSubmitForLogin = async () => {
    if (!phoneOtp) {
      setPhoneError("Please enter the OTP sent to your phone");
    }
    if (/[a-zA-Z]/.test(phoneOtp)) {
      setPhoneError("OTP should only contain numbers");
      return;
    }

    if (phoneOtp.length < 4) {
      setPhoneError("Please enter a valid OTP");
      return;
    }

    const payload = {
      userId: route.params.userID,
      email: route.params.email,
      phoneOtp: phoneOtp,
    };

    try {
      setLoading(true);
      const response = await dispatch(verifyLoginOtp(payload));
      console.log("Verification Response:", JSON.stringify(response));

      if (response?.payload?.success) {
        console.log("Verification successful");
        await dispatch(setUser(response?.payload?.data?.user));
        await dispatch(setToken(response?.payload?.data?.accessToken));

        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "MainApp" }],
          })
        );
      } else {
        console.error("Verification failed:", response?.payload?.data?.message);
        setPhoneError(response?.payload?.data?.message);
      }
    } catch (error) {
      console.error("Verification Error:", error);
      setPhoneError("Verification failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };
  const handleSubmitForForgotPassword = async () => {
    if (!emailOtp) {
      setEmailError("Please enter the OTP sent to your email");
    }
    if (/[a-zA-Z]/.test(emailOtp)) {
      setEmailError("OTP should only contain numbers");
      return;
    }
    if (emailOtp.length < 4) {
      setEmailError("Please enter a valid OTP");
      return;
    }
    const payload = {
      email: route.params.email,
      emailOtp: emailOtp,
    };

    try {
      setLoading(true);
      const response = await dispatch(verifyForgetPasswordOtp(payload));
      console.log("Verification Response:", JSON.stringify(response));

      if (response?.payload?.success) {
        console.log("Verification successful");
        navigation.navigate("ResetPasswordScreen", {
          userId: response?.payload?.data?.userId,
        });
      } else {
        console.error("Verification failed:", response?.payload?.data?.message);
        setEmailError(response?.payload?.data?.message);
      }
    } catch (error) {
      console.error("Verification Error:", error);
      setEmailError("Verification failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior="padding"
      style={{ flex: 1 }}
      >
    <SafeAreaView style={styles.container}>
      <View style={styles.inner}>
        <BackButton />

        <Text style={styles.title}>{heading}</Text>
        <Text style={styles.subtitle}>{subheading}</Text>

        {/* Progress Line */}
        {showProgressLine && (
          <View style={styles.progressContainer}>
            <View style={styles.progressLine} />
          </View>
        )}

        {showEmailVerification && (
          <>
            <InputField
              label="OTP sent on mail*"
              placeholder="Enter OTP"
              keyboardType="number-pad"
              maxLength={4}
              value={emailOtp}
              onChangeText={(val) => {
                setEmailOtp(val);
                if (emailError) setEmailError("");
              }}
              error={emailError}
            />
            <View style={styles.timerRow}>
              <Text style={styles.timerText}>{formatTime(emailTimer)}</Text>
              <TouchableOpacity
                disabled={emailTimer > 0}
                onPress={() => setEmailTimer(30)}
              >
                <Text
                  style={[styles.resendText, emailTimer > 0 && styles.disabled]}
                >
                  Resend OTP
                </Text>
              </TouchableOpacity>
            </View>
          </>
        )}

        {showPhoneVerification && (
          <>
            <InputField
              label="OTP sent on phone*"
              placeholder="Enter OTP"
              maxLength={4}
              keyboardType="number-pad"
              value={phoneOtp}
              onChangeText={(val) => {
                setPhoneOtp(val);
                if (phoneError) setPhoneError("");
              }}
              error={phoneError}
            />
            <View style={styles.timerRow}>
              <Text style={styles.timerText}>{formatTime(phoneTimer)}</Text>
              <TouchableOpacity
                disabled={phoneTimer > 0}
                onPress={() => setPhoneTimer(30)}
              >
                <Text
                  style={[styles.resendText, phoneTimer > 0 && styles.disabled]}
                >
                  Resend OTP
                </Text>
              </TouchableOpacity>
            </View>
          </>
        )}

        <TouchableOpacity
          disabled={loading}
          onPress={
            route.params?.type === "signup"
              ? handleSubmitForSignUp
              : route?.params?.type === "forgotPassword"
              ? handleSubmitForForgotPassword
              : route.params?.type === "socialSignup"
              ? handleSubmitForSocialSignup
              : handleSubmitForLogin
          }
          style={styles.submitButton}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.submitText}>Submit</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  inner: { padding: 20, flex: 1 },
  title: { fontSize: 22, fontWeight: "700", marginTop: 10, marginBottom: 8 },
  subtitle: { fontSize: 14, color: "gray", marginBottom: 20 },
  progressContainer: {
    height: 3,
    backgroundColor: "#eee",
    marginBottom: 30,
    alignItems: "flex-end",
  },
  progressLine: {
    height: 3,
    width: "50%",
    backgroundColor: "#2E64FE",
    borderRadius: 50,
  },
  timerRow: {
    flexDirection: "row",
    justifyContent: "space-between",
   
    marginBottom: 30,
  },
  timerText: {
    color: "#2E64FE",
    fontWeight: "500",
  },
  resendText: {
    fontWeight: "500",
  },
  disabled: {
    color: "#ccc",
  },
  submitButton: {
    marginTop: "auto",
    backgroundColor: "#2E64FE",
    paddingVertical: 16,
    borderRadius: 10,
    alignItems: "center",
  },
  submitText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
});

export default TwoFactorScreen;
