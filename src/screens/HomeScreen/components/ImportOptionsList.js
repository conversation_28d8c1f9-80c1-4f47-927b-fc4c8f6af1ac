import React from "react";
import { TouchableOpacity, View, Image } from "react-native";
import MyText from "../../../components/MyText";
import icons from "../../../assets/icons";
import commonStyles from "../../../assets/commonStyles";

const ImportOptionsList = React.memo(
  ({ importOptions, handleFetchContacts }) => (
    <>
      {importOptions.map((option, index) => (
        <TouchableOpacity
          key={index}
          onPress={() => handleFetchContacts(option.action)}
          style={styles.importOptionItem}
        >
          <View style={[commonStyles.row, styles.modalOption]}>
            <View style={commonStyles.rowWithoutSpaceBetween}>
              <Image
                source={option.icon}
                style={commonStyles.smallIcon}
                resizeMode="contain"
              />
              <MyText
                h6
                center
                children={option.name}
                style={styles.optionText}
              />
            </View>
            <Image
              source={icons.rightArrowIcon}
              style={commonStyles.extraSmallIcon}
              resizeMode="contain"
            />
          </View>
        </TouchableOpacity>
      ))}
    </>
  )
);

const styles = {
  importOptionItem: {
    marginVertical: 10,
  },
  modalOption: {},
  optionText: {
    alignSelf: "left",
  },
};

export default ImportOptionsList;
