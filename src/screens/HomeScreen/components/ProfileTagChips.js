import React, { useCallback } from "react";
import { View, ScrollView } from "react-native";
import MyText from "../../../components/MyText";
import ChipSelector from "../../../components/ChipSelector";

const ProfileTagChips = React.memo(({
  activeFilter,
  profileOptions,
  tagOptions,
  selectedProfile,
  setSelectedProfile,
  selectedTag,
  setSelectedTag,
}) => {
  const handleProfileSelect = useCallback(
    (value) => {
      setSelectedProfile(value === selectedProfile ? null : value);
    },
    [selectedProfile, setSelectedProfile]
  );

  const handleTagSelect = useCallback(
    (value) => {
      setSelectedTag(value === selectedTag ? null : value);
    },
    [selectedTag, setSelectedTag]
  );

  if (activeFilter === "profile") {
    if (profileOptions.length === 0) {
      return (
        <View style={styles.emptyStateContainer}>
          <MyText p children={"No Profiles Created"} />
        </View>
      );
    }
    return (
      <View style={styles.chipsContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <ChipSelector
            options={profileOptions}
            selectedValue={selectedProfile}
            onSelect={handleProfileSelect}
            containerStyle={styles.chipSelectorContainer}
          />
        </ScrollView>
      </View>
    );
  }

  if (activeFilter === "tag") {
    if (tagOptions.length === 0) {
      return (
        <View style={styles.emptyStateContainer}>
          <MyText p children={"No Tags Created"} />
        </View>
      );
    }
    return (
      <View style={styles.chipsContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <ChipSelector
            options={tagOptions}
            selectedValue={selectedTag}
            onSelect={handleTagSelect}
            containerStyle={styles.chipSelectorContainer}
          />
        </ScrollView>
      </View>
    );
  }

  return null;
});

const styles = {
  chipsContainer: {
    paddingHorizontal: 20,
  },
  emptyStateContainer: {
    paddingHorizontal: 20,
    marginTop: 10,
  },
  chipSelectorContainer: {
    width: "100%",
    gap: 10,
  },
};

export default ProfileTagChips;
