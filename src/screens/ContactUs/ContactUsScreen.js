import React, { useState, useEffect } from "react";
import {
  SafeAreaView,
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Image,
  KeyboardAvoidingView,
  Platform,
  Text,
  Dimensions,
} from "react-native";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import colors from "../../assets/colors";
import { useNavigation } from "@react-navigation/native";
import ChatBubble from "./components/ChatBubble";
import socketServices from "../../utils/socketServices";
import { useDispatch, useSelector } from "react-redux";
import { getUserChat } from "../../redux/features/mainSlice";

const ContactUsScreen = () => {
  const navigation = useNavigation();
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState("");

  const USER_ID = useSelector((state) => state.auth.user?._id);
  const dispatch = useDispatch();

  useEffect(() => {
    const initialize = async () => {
      await socketServices.initializeSocket();

      socketServices.on(USER_ID, (data) => {
        console.log("Raw socket data:", data);
        console.log("typeof data:", typeof data);

        try {
          const parsedData = typeof data === "string" ? JSON.parse(data) : data;

          // Adjust if it's wrapped inside 'data' key
          const innerData = parsedData?.data || parsedData;

          if (!innerData || !innerData.message) {
            console.error("Invalid socket data format:", parsedData);
            return;
          }

          console.log("Parsed inner data:", innerData);

          // Build message
          const incomingMessage = {
            id: innerData._id,
            text: innerData.message,
            time: new Date(innerData.createdAt).toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            }),
            sender:
              innerData.sender_id === USER_ID
                ? "me"
                : innerData.type === "admin"
                ? "admin"
                : "me", // handle admin/user
          };

          // If sender is not me, add to messages
          if (innerData.sender_id._id !== USER_ID) {
            setMessages((prevMessages) => [incomingMessage, ...prevMessages]);

            // // Emit message seen acknowledgment
            // if (innerData._id) {
            //   socketServices.emit('message-seen', { message_id: innerData._id });
            //   console.log('Message seen emitted');
            // }
          } else {
            console.log("Ignored own message from socket.");
          }
        } catch (error) {
          console.error("Error parsing socket data:", error);
        }
      });
    };

    initialize();
    fetchChats();

    return () => {
      socketServices.removeListener(USER_ID);
      socketServices.disconnectSocket();
    };
  }, []);

  const fetchChats = async () => {
    try {
      const response = await dispatch(getUserChat());

      console.log("Fetched chats:", JSON.stringify(response));

      if (response?.payload?.success && Array.isArray(response.payload.data)) {
        const oldMessages = response.payload.data.map((item) => ({
          id: item._id,
          text: item.message,
          time: new Date(item.createdAt).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          }),
          sender: item.type === "user" ? "me" : "admin",
        }));

        const reversedMessages = oldMessages.reverse();

        setMessages(reversedMessages);
      }
    } catch (error) {
      console.error("Error fetching chats:", error);
    }
  };

  const sendMessage = () => {
    if (!input.trim()) return;

    const messageData = {
      message: input.trim(),
      senderId: USER_ID,
      type: "user",
    };

    console.log("Sending message via socket:", messageData);

    socketServices.emit("query_chat", messageData);

    const newMessage = {
      id: Date.now().toString(),
      text: input,
      time: new Date().toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      }),
      sender: "me",
    };

    setMessages((prevMessages) => [newMessage, ...prevMessages]);
    setInput("");
  };

  return (
    <View style={styles.container}>
      <Header
        title="Admin Chat"
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />

      <FlatList
        data={messages}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.chatContainer}
        renderItem={({ item }) => (
          <ChatBubble message={item} isMe={item.sender === "me"} />
        )}
        inverted
        ListEmptyComponent={
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              height: screenHeight - 200,
            }}
          >
            <Text style={{ color: "#888", textAlign: "center", width: "70%" }}>
              The chat is emptier than your coffee cup—let’s fill it up ☕
            </Text>
          </View>
        }
        showsVerticalScrollIndicator={false}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <View style={styles.inputContainer}>
          <TextInput
            placeholder="Type your message"
            placeholderTextColor={colors.grey}
            style={styles.input}
            value={input}
            onChangeText={setInput}
          />
          <TouchableOpacity onPress={sendMessage} style={styles.sendButton}>
            <Image
              source={icons.SendButtonIcon}
              resizeMode="contain"
              style={styles.sendIcon}
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

const screenHeight = Dimensions.get("window").height;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fefefe" },
  subheader: {
    marginTop: -20,
    marginBottom: 4,
    paddingLeft: 16,
  },
  chatContainer: {
    paddingTop: 8,
    paddingBottom: 80,
  },
  inputContainer: {
    flexDirection: "row",
    padding: 12,
    alignItems: "center",
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderTopColor: "#eee",
  },
  input: {
    flex: 1,
    backgroundColor: "#f2f2f2",
    borderRadius: 25,
    paddingHorizontal: 16,
    height: 48,
    fontSize: 14,
    fontFamily: "Metropolis-Regular",
    color: colors.black,
  },
  sendButton: {
    backgroundColor: colors.primary,
    width: 42,
    height: 42,
    borderRadius: 21,
    alignItems: "center",
    justifyContent: "center",
    marginLeft: 10,
  },
  sendIcon: {
    width: 18,
    height: 18,
    tintColor: "#fff",
  },
});

export default ContactUsScreen;
