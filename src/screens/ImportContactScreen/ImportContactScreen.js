import { StyleSheet, View, TouchableOpacity } from "react-native";
import React, { useEffect, useState, useMemo } from "react";
import ContactList from "../../components/ContactList";
import {
  formatContactData,
  groupContactsByAlphabet,
} from "../../utils/commonHelpers";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import { useDispatch, useSelector } from "react-redux";
import MyText from "../../components/MyText";
import { PrimaryButton } from "../../components/Button";
import { postImportedContacts } from "../../redux/features/contactSlice";
import AppLoader from "../../components/AppLoader";
import { showToast } from "../../utils/toastConfig";
import SearchBar from "../../components/SearchBar";
import SortOptionsBox from "../../components/SortOptionsBox";
import { sortOptions as allSortOptions } from "../../utils/constants";

const ImportContactScreen = ({ navigation, route }) => {
  const { contacts, source } = route.params;

  const dispatch = useDispatch();
  const importedContacts = useSelector(
    (state) => state.contactSlice.postImportedContacts
  );
  const user = useSelector((state) => state.auth.user);

  const [selectedContacts, setSelectedContacts] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSortBoxVisible, setSortBoxVisible] = useState(false);
  const [activeSort, setActiveSort] = useState("az");

  const formattedContacts = useMemo(() => {
    setIsLoading(true);
    const result = formatContactData(contacts, source);
    setIsLoading(false);
    return result;
  }, [contacts, source]);

  // console.log("🚀 ~ ImportContactScreen ~ formattedContacts:", formattedContacts);

  // const filteredContacts = useMemo(() => {
  //   if (!searchQuery.trim()) return formattedContacts;
  //   const query = searchQuery.trim().toLowerCase();
  //   return formattedContacts
  //     .map((section) => ({
  //       ...section,
  //       data: section.data.filter((contact) => {
  //         const firstName = (contact.firstName || "").toLowerCase();
  //         const middleName = (contact.middleName || "").toLowerCase();
  //         const lastName = (contact.lastName || "").toLowerCase();
  //         const email = (contact.email || "").toLowerCase();
  //         const phone = (contact.phone || "").toLowerCase();
  //         return (
  //           firstName.includes(query) ||
  //           lastName.includes(query) ||
  //           middleName.includes(query) ||
  //           email.includes(query) ||
  //           phone.includes(query)
  //         );
  //       }),
  //     }))
  //     .filter((section) => section.data.length > 0);
  // }, [formattedContacts, searchQuery]);

  const filteredContacts = useMemo(() => {
    const query = searchQuery.trim().toLowerCase();

    let contactsToUse = [...formattedContacts];

    if (activeSort === "az" || activeSort === "za") {
      contactsToUse = contactsToUse.map((section) => ({
        ...section,
        data: [...section.data].sort((a, b) => {
          const nameA = (a.firstName || "").toLowerCase();
          const nameB = (b.firstName || "").toLowerCase();
          if (nameA < nameB) return activeSort === "az" ? -1 : 1;
          if (nameA > nameB) return activeSort === "az" ? 1 : -1;
          return 0;
        }),
      }));
    }

    if (!query) return contactsToUse;

    return contactsToUse
      .map((section) => ({
        ...section,
        data: section.data.filter((contact) => {
          const firstName = (contact.firstName || "").toLowerCase();
          const middleName = (contact.middleName || "").toLowerCase();
          const lastName = (contact.lastName || "").toLowerCase();
          const email = (contact.email || "").toLowerCase();
          const phone = (contact.phone || "").toLowerCase();

          // Add city search functionality
          const cities = [];

          // Check home address city
          if (contact.addresses_home?.city) {
            cities.push(contact.addresses_home.city.toLowerCase());
          }

          // Check other address city (if available)
          if (contact.addresses_other?.city) {
            cities.push(contact.addresses_other.city.toLowerCase());
          }

          // Check additional addresses array (if available)
          if (Array.isArray(contact.addresses)) {
            contact.addresses.forEach((address) => {
              if (address?.city) {
                cities.push(address.city.toLowerCase());
              }
            });
          }

          const cityString = cities.join(" ");

          return (
            firstName.includes(query) ||
            lastName.includes(query) ||
            middleName.includes(query) ||
            email.includes(query) ||
            phone.includes(query) ||
            cityString.includes(query)
          );
        }),
      }))
      .filter((section) => section.data.length > 0);
  }, [formattedContacts, searchQuery, activeSort]);

  function handleSearchChange(text) {
    setSearchQuery(text);
  }

  const onImportPress = () => {
    if (selectedContacts.length > 0) {
      setIsLoading(true);

      const selectedFullContacts = formattedContacts
        .flatMap((section) => section.data)
        .filter((contact) => {
          return selectedContacts.includes(contact.id);
        });

      dispatch(postImportedContacts(selectedFullContacts))
        .then(() => {
          showToast("success", "Contacts imported successfully.");
          navigation.navigate("MainApp", {
            screen: "Home Tabs",
            params: { screen: "Home" },
          });
        })
        .catch((error) => {
          console.error("Error importing contacts:", error);
          showToast("error", "Failed to import contacts. Please try again.");
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else {
      showToast("error", "Please select at least one contact to import.");
    }
  };

  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedContacts([]);
    } else {
      const allContactIds = formattedContacts
        .flatMap((section) => section.data)
        .map((contact) => contact.id);
      setSelectedContacts(allContactIds);
    }
    setSelectAll(!selectAll);
  };

  const handleSortOption = (option) => {
    setSortBoxVisible(false);
    if (!option) {
      setActiveSort(null);
      return;
    }
    setActiveSort(option.value);
  };

  const sortOptions = allSortOptions.filter(
    (opt) => opt.value === "az" || opt.value === "za"
  );

  // console.log("🚀 ~ ImportContactScreen ~ filtereed:", filteredContacts);

  return (
    <View style={{ flex: 1, backgroundColor: "#F9F7F7" }}>
      <Header
        title={`Import Contacts`}
        leftIcon={icons?.backwBg}
        onPressLeft={() => navigation.goBack()}
        pb={20}
        textCenter
      />
      <View style={{ paddingHorizontal: 15 }}>
        <SearchBar
          placeholder="Search here"
          value={searchQuery}
          onChangeText={handleSearchChange}
          // onPressRightIcon2={() => setSortBoxVisible((prev) => !prev)}
        />
        {isSortBoxVisible && (
          <SortOptionsBox
            options={sortOptions}
            onSelect={handleSortOption}
            style={[styles.sortBoxOverlay, { right: 60 }]}
            optionStyle={styles.sortBoxOption}
            optionTextStyle={styles.optionText}
            activeValue={activeSort}
            allowDeselect={true}
          />
        )}
      </View>

      <AppLoader isLoading={isLoading || importedContacts?.loading} />
      <View style={styles.listHeader}>
        <MyText semibold h5 children={`Contacts`} />
        <TouchableOpacity onPress={toggleSelectAll}>
          <MyText p children={selectAll ? "Deselect All" : "Select All"} />
        </TouchableOpacity>
      </View>
      <ContactList
        contacts={filteredContacts}
        mode="select"
        selectedContacts={selectedContacts}
        showAlphabetList={false}
        setSelectedContacts={setSelectedContacts}
        microsoft={true}
      />
      <PrimaryButton
        onPress={onImportPress}
        title={"Import"}
        style={[styles.btn]}
      />
    </View>
  );
};

export default ImportContactScreen;

const styles = StyleSheet.create({
  listHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 10,
    paddingHorizontal: 20,
    alignItems: "center",
  },
  btn: {
    bottom: 30,
  },
  sortBoxOverlay: {
    position: "absolute",
    top: 80,
    right: 10,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 150,
  },
  sortBoxOption: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
  optionText: {
    alignSelf: "left",
  },
});
