import {
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import React, { useEffect, useState } from "react";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import { useFocusEffect } from "@react-navigation/native";
import {
  addContactsToProfile,
  getProfileMembersByProfileId,
  getProfileNameData,
  moveOrRemoveMemberFromProfile,
} from "../../redux/features/SharingProfileSlice";
import MyText from "../../components/MyText";
import { useSelector, useDispatch } from "react-redux";
import { setProfileContactsData } from "../../redux/features/mainSlice";
import AppLoader from "../../components/AppLoader";
import InputField from "../../components/InputField";
import { PrimaryButton } from "../../components/Button";
import colors from "../../assets/colors";
import ContactCard from "../../components/ContactCard";
import commonStyles from "../../assets/commonStyles";
import { showToast } from "../../utils/toastConfig";
import { SCREEN_HEIGHT } from "../../utils/constants";

const EditSharingProfileScreen = ({ navigation, route }) => {
  const [errors, setErrors] = useState({});
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [profileName, setProfileName] = useState("");
  const [selectedContacts, setSelectedContacts] = useState([]);
  const profileContactsData = useSelector(
    (state) => state.mainSlice.profileContactsData
  );
  const profileMembersState = useSelector(
    (state) => state.sharingProfileSlice.getProfileMembersByProfileId
  );

  const { data: profileMembersData, loading: profileMembersLoading } =
    profileMembersState;

  useEffect(() => {
    // If profileId is passed in route params, fetch profile data
    if (route?.params?.profileId) {
      dispatch(getProfileMembersByProfileId(route.params.profileId));
    }
  }, [route?.params?.profileId, dispatch]);
  console.log(
    "🚀 ~ MoveToProfileScreen ~ profileMembersData:",
    JSON.stringify(profileMembersData, null, 2)
  );

  // Update selectedContacts from AddContactsScreen or API
  useEffect(() => {
    // Get profile members from API
    const profileMembers =
      profileMembersData?.data?.members?.map((item) => ({
        id: item._id,
        name: `${item.firstName || ""} ${item.middleName || ""} ${item.lastName || ""}`.trim(),
        phone: item?.phoneNumbers?.[0]?.number || item?.emails?.[0]?.address,
        imgUrl: item?.profile_image || "",
      })) || [];
    setProfileName(profileMembersData?.data?.profile_name);
    // Get additional contacts from AddContactsScreen
    const additionalContacts = (profileContactsData || []).filter(
      (contact) => !profileMembers.some((member) => member.id === contact.id)
    );

    // Combine: profile members first, then additional contacts
    setSelectedContacts([...profileMembers, ...additionalContacts]);
  }, [profileContactsData, profileMembersData]);

  // Navigate to contact selection screen, passing selectedContacts
  const navigateToContactSelection = () => {
    navigation.navigate("AddContactsScreen", {
      fromProfile: route?.params?.profileId,
      selectedContacts: selectedContacts,
    });
  };

  // Save handler: update profile with all selected contacts and profile name
  const handleSaveProfile = async () => {
    console.log("Selected Contacts:");
    if (!profileName.trim()) {
      setErrors((prev) => ({
        ...prev,
        profileName: "Profile name is required",
      }));
      return;
    }
    if (!selectedContacts.length) {
      showToast("error", "Error", "Please select at least one contact.");

      return;
    }
    const updateApiBody = {
      profile_name: profileName,
      addInProfile: selectedContacts.map((contact) => ({
        memberId: contact.id,
      })),
    };
    console.log(
      "🚀 ~ handleSaveProfile ~ updateApiBody:",
      JSON.stringify(updateApiBody, null, 2)
    );
    console.log(
      "🚀 ~ handleSaveProfile ~ route?.params?.profileId:",
      route?.params?.profileId
    );
    try {
      const response = await dispatch(
        addContactsToProfile({
          profile_management_id: route?.params?.profileId,
          body: updateApiBody,
        })
      );
      console.log("🚀 ~ handleSaveProfile ~ response:", response);
      const isSuccess =
        response.success || (response.payload && response.payload.success);
      showToast(
        isSuccess ? "success" : "error",
        response?.payload?.message ||
          response?.message ||
          (isSuccess
            ? "Profile updated successfully."
            : "Failed to update profile.")
      );
      if (isSuccess) navigation.goBack();
    } catch (error) {
      showToast(
        "error",
        error?.message || "Something went wrong while updating profile."
      );
    }
  };
  const handleRemove = (id) => {
    setSelectedContacts((prev) => prev.filter((contact) => contact.id !== id));
  };


  console.log("🚀 ~ EditSharingProfileScreen ~ selectedContacts:", selectedContacts);
  
  return (
    <View style={styles.container}>
      <Header
        title="Edit Profile"
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />
      <AppLoader isLoading={isLoading || profileMembersLoading} />

      <View
        style={{
          backgroundColor: colors.white,
          paddingHorizontal: 14,
          paddingTop: 12,
        }}
      >
        <View style={{ marginTop: 10 }}>
          <InputField
            label="Profile Name*"
            value={profileName}
            onChangeText={(text) => {
              setProfileName(text);
              if (text.trim() === "") {
                setErrors((prev) => ({
                  ...prev,
                  profileName: "Profile name is required",
                }));
              } else {
                setErrors((prev) => ({ ...prev, profileName: "" }));
              }
            }}
            error={errors.profileName}
            placeholder="Profile name"
          />
          <View
            style={{
              alignSelf: "flex-end",
              flexDirection: "row",
              gap: 6,
            }}
          >
            <Image
              source={icons.editIcon}
              style={{ width: 18, height: 18 }}
              resizeMode="contain"
            />
            <MyText
              onPress={() => {
                navigation.navigate("ProfileCompletionScreen", {
                  profileId: route?.params?.profileId,
                });
              }}
              underline
              p
              children={"Edit Profile Fields"}
            />
          </View>
          <MyText
            semibold
            h6
            children={"Contacts"}
            style={styles.contactsHeader}
          />
          <View style={{ height: "68%" }}>
            <FlatList
              data={selectedContacts}
              showsVerticalScrollIndicator={false}
              renderItem={({ item }) => (
                <ContactCard
                  name={item.name}
                  phone={item.phone}
                  mode={"cancel"}
                  onCancel={() => handleRemove(item.id)}
                  imgUrl={item.imgUrl || ""}
                />
              )}
              keyExtractor={(item) => item.id}
              // ListFooterComponent={() => (
              //   <
              // )}
            />
            <MyText
              underline
              medium
              h7
              children={"+Add More"}
              style={{ marginTop: 12, alignSelf: "flex-end" }}
              onPress={navigateToContactSelection}
            />
          </View>
        </View>
      </View>
      <View style={{ position: "absolute", bottom: 20, alignSelf: "center" }}>
        <PrimaryButton
          title="Save"
          onPress={() => handleSaveProfile()}
          style={{ marginTop: 20 }}
        />
      </View>
    </View>
  );
};

export default EditSharingProfileScreen;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  contactsHeader: {
    marginBottom: 10,
    marginTop: 20,
  },
});
