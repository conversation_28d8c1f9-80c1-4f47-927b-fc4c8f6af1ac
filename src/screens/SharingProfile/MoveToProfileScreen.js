import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import Header from "../../components/Header";
import SearchBar from "../../components/SearchBar";
import ContactCard from "../../components/ContactCard";
import icons from "../../assets/icons";
import { PrimaryButton } from "../../components/Button";
import MyText from "../../components/MyText";
import BottomModal from "../../components/BottomModal";

import { useDispatch, useSelector } from "react-redux";
import {
  getProfileByManagementId,
  getProfileMembersByProfileId,
  getRestProfilesByProfileId,
  moveOrRemoveMemberFromProfile,
} from "../../redux/features/SharingProfileSlice";
import colors from "../../assets/colors";
import {
  getProfileIcon,
  SCREEN_HEIGHT,
  SCREEN_WIDTH,
} from "../../utils/constants";
import commonStyles from "../../assets/commonStyles";
import AppLoader from "../../components/AppLoader";
import { showToast } from "../../utils/toastConfig";
import SortOptionsBox from "../../components/SortOptionsBox";

const MoveToProfileScreen = ({ navigation, route }) => {
  const [selectedContacts, setSelectedContacts] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [contacts, setContacts] = useState([]);
  const [isModalVisible, setModalVisible] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [isSecondBoxVisible, setSecondBoxVisible] = useState(false);
  const [activeFilter, setActiveFilter] = useState(null);

  const dispatch = useDispatch();
  const profileMembersState = useSelector(
    (state) => state.sharingProfileSlice.getProfileMembersByProfileId
  );
  const restProfilesState = useSelector(
    (state) => state.sharingProfileSlice.getRestProfilesByProfileId
  );
  const moveOrRemoveMemberFromProfileState = useSelector(
    (state) => state.sharingProfileSlice.moveOrRemoveMemberFromProfile
  );
  const { data: profileMembersData, loading: profileMembersLoading } =
    profileMembersState;
  const { data: restProfilesData, loading: loadingRestProfiles } =
    restProfilesState;
  const {
    data: moveOrRemoveMemberFromProfileData,
    loading: loadingMoveOrRemove,
  } = moveOrRemoveMemberFromProfileState;
 
  const filterOptions = [
  { label: "Favourites", value: "favourites" },
];


  useEffect(() => {
    // If profileId is passed in route params, fetch profile data
    if (route?.params?.profileId) {
      console.log("profileId: ", route.params.profileId);
      
      dispatch(getProfileMembersByProfileId(route.params.profileId));
      dispatch(getRestProfilesByProfileId(route.params.profileId));
    }
  }, [route?.params?.profileId, dispatch]);

  useEffect(() => {
    // If API data is available, set contacts from API response
    if (profileMembersData?.data?.members) {
      setContacts(profileMembersData.data.members);
    }
  }, [profileMembersData]);

  const toggleSelect = (contact) => {
    const isAlreadySelected = selectedContacts.some(
      (c) => c._id === contact._id
    );
    if (isAlreadySelected) {
      setSelectedContacts(
        selectedContacts.filter((c) => c._id !== contact._id)
      );
    } else {
      setSelectedContacts([...selectedContacts, contact]);
    }
  };

  const handleMoveContact = () => {
    if (selectedContacts.length === 0) {
      showToast("error", "Error", "Please select at least one contact.");
      return;
    }
    setModalVisible(true);
  };
  const handleMoveContactToProfile = async (toProfileId) => {
    setModalVisible(false);

    const moveApiBody = {
      type: "add",
      from_profile_management_id: route.params.profileId,
      to_profile_management_id: toProfileId,
      addInProfile: selectedContacts.map((contact) => ({
        memberId: contact._id,
      })),
    };

    try {
      // Replace with your actual move API call, e.g. dispatch(moveContactsToProfile(moveApiBody))
      const response = await dispatch(
        moveOrRemoveMemberFromProfile(moveApiBody)
      );
      console.log(
        "MoveToProfileScreen moveOrRemoveMemberFromProfile response:",
        response
      );
      const isSuccess =
        response.success || (response.payload && response.payload.success);
      if (isSuccess) {
        showToast(
          "success",
          response?.payload?.message ||
            response?.message ||
            "Contacts moved successfully."
        );
      } else {
        showToast(
          "error",
          response?.payload?.message ||
            response?.message ||
            "Failed to move contacts."
        );
      }

      navigation.goBack();
    } catch (error) {
      showToast(
        "error",
        error?.message || "Something went wrong while moving contacts."
      );
    }
  };
  const handleRemove = async (toProfileId) => {
    if (selectedContacts.length === 0) {
      showToast("error", "Error", "Please select at least one contact.");
      return;
    }

    const moveApiBody = {
      type: "remove",
      from_profile_management_id: route.params.profileId,
      addInProfile: selectedContacts.map((contact) => ({
        memberId: contact._id,
      })),
    };

    try {
      // Replace with your actual move API call, e.g. dispatch(moveContactsToProfile(moveApiBody))
      const response = await dispatch(
        moveOrRemoveMemberFromProfile(moveApiBody)
      );
      console.log(
        "MoveToProfileScreen moveOrRemoveMemberFromProfile response:",
        response
      );
      const isSuccess =
        response.success || (response.payload && response.payload.success);
      if (isSuccess) {
        showToast(
          "success",
          response?.payload?.message ||
            response?.message ||
            "Contacts removed successfully."
        );
      } else {
        showToast(
          "error",
          response?.payload?.message ||
            response?.message ||
            "Failed to remove contacts."
        );
      }

      navigation.goBack();
    } catch (error) {
      showToast(
        "error",

        error?.message || "Something went wrong while removing contacts."
      );
    }
  };

  const getFullName = (contact) =>
    `${contact.firstName || ""} ${contact.middleName || ""} ${contact.lastName || ""}`.trim();

  const getPrimaryPhone = (contact) =>
    contact.phoneNumbers?.[0]?.number || "No phone";

  // const filteredContacts = contacts.filter((contact) =>
  //   getFullName(contact).toLowerCase().includes(searchQuery.toLowerCase())
  // );
  const filteredContacts = contacts.filter((contact) => {
  const matchesSearch = getFullName(contact).toLowerCase().includes(searchQuery.toLowerCase());
  const matchesFilter = !activeFilter || (activeFilter === "favourites" && contact.is_favorite);
  return matchesSearch && matchesFilter;
});

  

  const handleFilterSelect = (option) => {
    // Handle filter selection logic here
    console.log("Selected filter option:", option);
    
    if(option?.value.toLowerCase() === activeFilter) {
      setActiveFilter(null); 
    }
    else {
      setActiveFilter(option?.value.toLowerCase()); 
    }
    setSecondBoxVisible(false);
  }

  return (
    <View style={styles.container}>
      <Header
        title="Home"
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />
      <AppLoader
        isLoading={
          profileMembersLoading || loadingRestProfiles || loadingMoveOrRemove
        }
      />
      <View style={{ paddingHorizontal: 20 }}>
        <SearchBar
          placeholder="Search here"
          value={searchQuery}
          onChangeText={setSearchQuery}
          onPressRightIcon1={() => setSecondBoxVisible(!isSecondBoxVisible)}
        />
                  {isSecondBoxVisible && (
            <SortOptionsBox
              options={filterOptions}
              onSelect={handleFilterSelect}
              style={[styles.sortBoxOverlay, { right: 20 }]}
              optionStyle={styles.sortBoxOption}
              optionTextStyle={styles.optionText}
              activeValue={activeFilter}
              allowDeselect={true}
            />
          )}
      </View>

      <View style={styles.contactHeader}>
        <MyText bold>Contacts</MyText>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <MyText
            p
            underline
            regular
            onPress={() => {
              const allSelected =
                filteredContacts.length > 0 &&
                filteredContacts.every((fc) =>
                  selectedContacts.some((sc) => sc._id === fc._id)
                );
              if (allSelected) {
                setSelectedContacts(
                  selectedContacts.filter(
                    (sc) => !filteredContacts.some((fc) => fc._id === sc._id)
                  )
                );
              } else {
                const newSelected = [
                  ...selectedContacts.filter(
                    (sc) => !filteredContacts.some((fc) => fc._id === sc._id)
                  ),
                  ...filteredContacts.filter(
                    (fc) => !selectedContacts.some((sc) => sc._id === fc._id)
                  ),
                ];
                setSelectedContacts(newSelected);
              }
            }}
          >
            {filteredContacts.length > 0 &&
            filteredContacts.every((fc) =>
              selectedContacts.some((sc) => sc._id === fc._id)
            )
              ? "Deselect All"
              : "Select All"}
          </MyText>
        </View>
      </View>
      {filteredContacts.length > 0 && (
        <FlatList
          data={filteredContacts}
          keyExtractor={(item) => item._id}
          renderItem={({ item }) => (
            <ContactCard
              name={getFullName(item)}
              phone={getPrimaryPhone(item)}
              isSelected={selectedContacts.some((c) => c._id === item._id)}
              mode="select"
              onPress={() => toggleSelect(item)}
              imgUrl={item.profile_image}
            />
          )}
          contentContainerStyle={{ paddingBottom: 80 }}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text>No Contacts Found</Text>
            </View>
          }
        />
      )}
      <View style={styles.button}>
        <PrimaryButton
          title="Remove Contact"
          onPress={handleRemove}
          style={{
            width: SCREEN_WIDTH / 2 - 20, //30
            backgroundColor: colors.gray,
          }}
          textStyle={{ color: colors.black }}
        />
        <PrimaryButton
          title="Move Contact"
          onPress={handleMoveContact}
          style={{ width: SCREEN_WIDTH / 2 - 20 }} //30px
        />
      </View>
      <BottomModal
        isVisible={isModalVisible}
        onClose={() => setModalVisible(false)}
        title="Move Contacts To Profile"
      >
        <ScrollView style={{ height: SCREEN_HEIGHT * 0.4 }} showsVerticalScrollIndicator={false} nestedScrollEnabled>
          {restProfilesData?.data?.length ? (
            restProfilesData?.data?.map((profile, idx) => (
              <TouchableOpacity
                onPress={() => handleMoveContactToProfile(profile._id)}
                key={profile._id || idx}
                style={[
                  commonStyles?.rowWithoutSpaceBetween,
                  {
                    paddingHorizontal: 10,
                    paddingVertical: 12,
                    borderBottomWidth: 1,
                    borderColor: "#eee",
                    alignItems: "center",
                  },
                ]}
              >
                <Image
                  source={getProfileIcon(profile.profile_name)}
                  style={[
                    styles.icon,
                    { tintColor: colors.primary, resizeMode: "contain" },
                  ]}
                />
                <MyText>{profile.profile_name}</MyText>
              </TouchableOpacity>
            ))
          ) : (
            <MyText style={{ padding: 16 }}>No other profiles found.</MyText>
          )}
        </ScrollView>
      </BottomModal>
      
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  contactHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: "#f2f2f2",
    marginTop: 10,
  },
  contactLabel: {
    fontSize: 16,
    fontWeight: "600",
  },
  emptyContainer: {
    alignItems: "center",
    marginTop: 50,
  },
  button: {
    flexDirection: "row",
    bottom: 40,
    position: "absolute",
    marginHorizontal: 30,
    gap: 10,
    alignSelf: "center",
  },
  icon: {
    height: 14,
    width: 14,
    resizeMode: "contain",
  },
    sortBoxOverlay: {
    position: "absolute",
    top: 80,
    right: 10,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 150,
  },
  sortBoxOption: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
});

export default MoveToProfileScreen;
