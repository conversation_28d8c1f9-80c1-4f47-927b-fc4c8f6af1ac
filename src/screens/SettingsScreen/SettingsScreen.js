import React, { useState } from "react";
import {
  SafeAreaView,
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
} from "react-native";
import Header from "../../components/Header";
import MyText from "../../components/MyText";
import Avatar from "../../components/Avatar";
import SettingsTile from "./components/SettingsTile";
import colors from "../../assets/colors";
import icons from "../../assets/icons";
import { useDispatch, useSelector } from "react-redux";
import AppModal from "../../components/AppModal";
import { CommonActions, useNavigation } from "@react-navigation/native";
import { logout, logoutUser } from "../../redux/features/authSlice";
import { deleteAccount } from "../../redux/features/mainSlice";

const SettingsScreen = () => {
  const [modalVisibleForLogin, setModalVisibleForLogin] = useState({
    visible: false,
    title: "",
    description: "",
    singleButton: true,
    secondButtonText: "",
    firstButtonText: "",
    onClose: () =>
      setModalVisibleForLogin((prev) => ({ ...prev, visible: false })),
    onSubmit: null,
  });

  const user = useSelector((state) => state.auth.user);
  console.log("user in settings screen", user);

  const token = useSelector((state) => state.auth.token);
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const tiles = [
    {
      label: "Notification Settings",
      icon: icons.bellIcon,
      screen: "NotificationSettingsScreen",
    },
    { label: "FAQs", icon: icons.FaqIcon, screen: "FaqsScreen" },
    // { label: 'About US', icon: icons.AboutUsIcon, screen: 'AboutUsScreen' },
    {
      label: "Contact Us",
      icon: icons.contactUsIcon,
      screen: "ContactUsScreen",
    },
    // { label: 'Privacy Policy', icon: icons.privacyPolicyIcon, screen: 'PrivacyPolicyScreen' },
    // { label: 'Terms & Conditions', icon: icons.termsConditionsIcon, screen: 'TermsConditionsScreen' },
    {
      label: "Change Password",
      icon: icons.ChangePasswordIcon,
      screen: "ChangePasswordScreen",
    },
    { label: "Delete my account", icon: icons.deleteAccountIcon },
  ];

  const handleLogout = () => {
    // navigation.toggleDrawer();
    setModalVisibleForLogin({
      visible: true,
      title: "Logout",
      description: "Are you sure you want to logout?",
      btnTitle: "Logout",
      onClose: () =>
        setModalVisibleForLogin((prev) => ({ ...prev, visible: false })),
      onSubmit: async () => {
        try {
          const payload = {
            token: token,
          };

          console.log("Logout payload:", payload);

          const response = await dispatch(logout(payload));
          console.log("Logout response:", JSON.stringify(response));
          if (response.payload.success) {
            console.log("Logout successful");
          } else {
            console.log("Logout failed");
          }
        } catch (error) {
          console.error("Logout error:", error);
        }
        // return;
        dispatch(logoutUser());
        setModalVisibleForLogin((prev) => ({ ...prev, visible: false }));
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "Onboarding" }],
          })
        );
      },
    });
  };

  const handleDeleteAccount = () => {
    setModalVisibleForLogin({
      visible: true,
      title: "Delete Account",
      description: "Are you sure you want to delete your account?",
      btnTitle: "Delete",
      onClose: () =>
        setModalVisibleForLogin((prev) => ({ ...prev, visible: false })),
      onSubmit: async () => {
        // Add your delete account logic here
        try {
          const response = await dispatch(deleteAccount());
          console.log("Delete account response:", JSON.stringify(response));
          if (response.payload.success) {
            console.log("Account deleted successfully");
          } else {
            console.log("Failed to delete account");
          }
        } catch (error) {
          console.error("Error deleting account:", error);
        } finally {
          dispatch(logoutUser());
          setModalVisibleForLogin((prev) => ({ ...prev, visible: false }));
          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [{ name: "Onboarding" }],
            })
          );
        }
      },
    });
  };

  return (
    <View style={styles.container}>
      <Header
        title="Settings"
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />

      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => {
          navigation.navigate("ContactDetailsScreen", {
            id: user?._id,
            personal: true,
          });
        }}
        style={styles.profileContainer}
      >
        <Avatar url={user?.profile_image} name={user?.firstName} size={55} />
        <View style={{ marginLeft: 12 }}>
          <MyText bold>{user?.firstName + " " + user?.lastName}</MyText>
          <View>
            <MyText color="#888">View Account</MyText>
          </View>
        </View>
      </TouchableOpacity>

      <View style={styles.tilesWrapper}>
        {tiles.map((item, index) => (
          <SettingsTile
            key={index}
            label={item.label}
            icon={item.icon}
            onPress={() => {
              if (item.label === "Delete my account") {
                handleDeleteAccount();
                return;
              }
              navigation.navigate(item.screen);
            }}
          />
        ))}
      </View>

      <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
        <Image
          source={icons.logoutIcon}
          style={[{ tintColor: "#FF4D4D", height: 18, width: 18 }]}
        />
        <MyText bold color={colors.red} style={{ marginLeft: 6 }}>
          Log Out
        </MyText>
      </TouchableOpacity>
      <AppModal {...modalVisibleForLogin} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  profileContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    backgroundColor: "#fff",
    // borderBottomWidth: 1,
    // borderColor: '#eee',
  },
  tilesWrapper: {
    flex: 1,
    marginTop: 10,
  },
  logoutButton: {
    flexDirection: "row",
    alignSelf: "flex-start",
    backgroundColor: "#FFECEC",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: "center",
    margin: 30,
  },
});

export default SettingsScreen;
