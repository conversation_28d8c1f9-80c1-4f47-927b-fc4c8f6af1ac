import React, { useRef, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  KeyboardAvoidingView,
} from "react-native";
import { showToast } from "../../utils/toastConfig";
import Header from "../../components/Header";
import InputField from "../../components/InputField";
import PhoneNumberField from "../../components/PhoneNumberField";
import colors from "../../assets/colors";
import icons from "../../assets/icons";
import { PrimaryButton } from "../../components/Button";
import { useDispatch } from "react-redux";
import {
  addManualContact,
  getContacts,
} from "../../redux/features/contactSlice";
import AppLoader from "../../components/AppLoader";
import MyText from "../../components/MyText";

const AddManualContactScreen = ({ navigation }) => {
  const phoneInput = useRef(null);
  const [form, setForm] = useState({
    firstName: "",
    middleName: "",
    lastName: "",
    email: "",
    phone: "",
    countryCode: "QA",
    callingCode: "971",
  });
  const [errors, setErrors] = useState({});

  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);

  const handleChange = (key, value) => {
    setForm((prev) => ({ ...prev, [key]: value }));
    setErrors((prev) => ({ ...prev, [key]: null }));
  };

  const validate = () => {
    const newErrors = {};
    const emailRegex = /\S+@\S+\.\S+/;

    if (!form.firstName.trim()) newErrors.firstName = "First name is required";
    if (!form.lastName.trim()) newErrors.lastName = "Last name is required";
    if (form.middleName && !form.middleName.trim()) {
      newErrors.middleName = "Middle name cannot be just spaces";
    }
    if (form.email && !form.email.trim()) {
      newErrors.email = "Email cannot be just spaces";
    }
    if (!form.email.trim()) {
      newErrors.email = "Email is required";
    }
    if (form.email && !emailRegex.test(form.email)) {
      newErrors.email = "Enter a valid email address";
    }

    if (!form.phone) {
      newErrors.phone = "Phone number is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (validate()) {
      console.log("Submitted Contact:", form);

      const payload = {
        firstName: form.firstName,
        ...(form.middleName?.trim() && { middleName: form.middleName.trim() }),
        lastName: form.lastName,
        source: "manual",
        email: form.email,
        phone_number: form.phone,
        country_code: form.callingCode,
        calling_code: form.countryCode,
      };

      console.log("Payload:", payload);

      try {
        setLoading(true);
        const response = await dispatch(addManualContact(payload));
        console.log(
          "Response from addManualContact:",
          JSON.stringify(response)
        );
        if (response.payload.success) {
          console.log("Contact added successfully");
          showToast("success", "Contact added successfully");
          const item = response.payload.data;
          dispatch(getContacts());
          navigation.replace("ContactDetailsScreen", {
            id: item._id,
          });
        } else {
          console.error(
            "Failed to add contact:",
            response.payload.data.message
          );
        }
      } catch (error) {
        console.error("Error adding contact:", error);
        showToast("error", "Failed to add contact");
      } finally {
        setLoading(false);
      }
    } else {
      showToast("error", "Please fix validation errors");
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior="padding"
      >
    <View style={styles.container}>
      <Header
        title="New Contact"
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />
      <ScrollView contentContainerStyle={styles.scroll}>
        <InputField
          label="First Name*"
          placeholder="Enter First Name"
          value={form.firstName}
          onChangeText={(val) => handleChange("firstName", val)}
          error={errors.firstName}
        />

        {/* MiddleName */}
        <InputField
          label="Middle Name"
          placeholder="Enter Middle Name"
          value={form.middleName}
          onChangeText={(val) => handleChange("middleName", val)}
          error={errors.middleName}
        />

        <InputField
          label="Last Name*"
          placeholder="Enter Last Name"
          value={form.lastName}
          onChangeText={(val) => handleChange("lastName", val)}
          error={errors.lastName}
        />
        <InputField
          label="Email*"
          placeholder="Enter your email"
          value={form.email}
          onChangeText={(val) => handleChange("email", val)}
          error={errors.email}
        />
        <MyText p medium style={styles.label}>
          Phone Number*
        </MyText>
        <PhoneNumberField
          value={form.phone}
          phoneInputRef={phoneInput}
          onChangeRaw={(val) => handleChange("phone", val)}
          onCodeChange={(val) => {
            console.log("PhoneNumberField onCodeChange:", val);
            handleChange("callingCode", val);
          }}
          onCountryCodeChange={(val) => {
            console.log("PhoneNumberField onCountryCodeChange:", val);
            handleChange("countryCode", val);
          }}
          error={errors.phone}
          setError={(msg) => setErrors((prev) => ({ ...prev, phone: msg }))}
          countryCodeProp={form.countryCode}
          callingCodeProp={form.callingCode}
        />
      </ScrollView>
      <PrimaryButton
        title="Add Contact"
        onPress={handleSubmit}
        style={styles.button}
      />
      <AppLoader isLoading={loading} />
    </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: colors.white },
  scroll: { padding: 20, paddingBottom: 40 },
  label: {
    marginBottom: 6,
    // marginTop: 10,
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginTop: 4,
    marginBottom: 10,
  },
  button: {
    marginVertical: 30,
  },
  buttonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
});

export default AddManualContactScreen;
