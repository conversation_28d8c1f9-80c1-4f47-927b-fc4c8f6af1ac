import React, { useState } from "react";
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  TouchableOpacity,
  Image,
  KeyboardAvoidingView,
} from "react-native";
import ProfilePicture from "../../components/ProfilePicture";
import EditableField from "./components/EditableField";
import DropdownSection from "./components/DropdownSection";
import { PrimaryButton } from "../../components/Button";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import commonStyles from "../../assets/commonStyles";
import { useNavigation } from "@react-navigation/native";
import { showToast } from "../../utils/toastConfig";
import { formatDate } from "../../utils/commonHelpers";
import MyText from "../../components/MyText";
import DatePicker from "../../components/DatePicker";
import TagManager from "./components/TagManager";
import { updateContact } from "../../redux/features/contactSlice";
import AppLoader from "../../components/AppLoader";
import { useDispatch } from "react-redux";
import CustomDropDown from "../../components/SelectField";
import colors from "../../assets/colors";
import PhoneNumberField from "../../components/PhoneNumberField";

const EditContactScreen = ({ route }) => {
  const { contact } = route.params;
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  const [form, setForm] = useState({
    firstName: contact.firstName || "",
    middleName: contact.middleName || "",
    lastName: contact.lastName || "",
    phones: contact.phoneNumbers || [],
    emails: contact.emails || [],
    profileImage: contact.profile_image || "",
    addressHomeApartment: contact.addresses_home?.apartment || "",
    addressHomeStreet: contact.addresses_home?.street || "",
    addressHomeCity: contact.addresses_home?.city || "",
    addressHomeState: contact.addresses_home?.state || "",
    addressHomeCountry: contact.addresses_home?.country || "",
    addressHomePostalCode: contact.addresses_home?.postalCode || "",
    addresses: contact.addresses || [],
    birthday: contact.dateOfBirth || "",
    gender: contact.gender || "Other",
    maritalStatus: contact.maritalStatus || "",
    spouseName: contact.spouseName || "",
    Religion: contact.religion || "",
    timeZone: contact.timeZone || "",
    nationality: contact.nationality || "",
    nickname: contact.nickname || "",
    languages: contact.languages?.join(", ") || "",
    hobbies: contact.hobbies?.join(", ") || "",
    tagsArray: contact.tagsArray || [],
    notes: contact.notes || "",
    emergencyContact: contact.emergency_contact?.phoneNumber || "",
    emergencyContactName: contact.emergency_contact?.contactName || "",
    emergencyContactRelation: contact.emergency_contact?.relationship || "",
    healthInsurancePolicyNumber: contact.healthInsurance?.policyNumber || "",
    healthInsuranceExpiryDate: contact.healthInsurance?.expirationDate || "",
    healthInsuranceEffectiveDate: contact.healthInsurance?.effectiveDate || "",
    billingAddressOfficeBuilding: contact.billing_address?.officeBuilding || "",
    billingAddressStreet: contact.billing_address?.street || "",
    billingAddressCity: contact.billing_address?.city || "",
    billingAddressState: contact.billing_address?.state || "",
    billingAddressPostalCode: contact.billing_address?.postalCode || "",
    billingAddressCountry: contact.billing_address?.country || "",
    accountDetailsName: contact.account_details?.name || "",
    accountDetailsBank: contact.account_details?.bank || "",
    accountDetailsAccountNumber: contact.account_details?.accountNumber || "",
    accountDetailsIfscCode: contact.account_details?.ifscCode || "",
    accountDetailsPaypalEmail: contact.account_details?.paypalEmail || "",
    cardDetailsNameOnCard: contact.card_details?.nameOnCard || "",
    cardDetailsCardNumber: contact.card_details?.cardNumber || "",
    cardDetailsExpiryDate: contact.card_details?.expiryDate || "",
    cryptoWalletAddress: contact.crypto_wallet?.wallet_address || "",
    cryptoWalletType: contact.crypto_wallet?.wallet_type || "",
    businessDetailsJobTitle: contact.business_details?.jobTitle || "",
    businessDetailsCompanyName:
      contact.business_details?.companyInformation?.company_name || "",
    businessDetailsDepartment: contact.business_details?.department || "",
    businessDetailsWorkContactPhoneNumber:
      contact.business_details?.workContact?.phoneNumber || "",
    businessDetailsWorkContactFax:
      contact.business_details?.workContact?.fax || "",
    businessDetailsWorkContactEmail:
      contact.business_details?.workContact?.email || "",
    businessDetailsResume: contact.business_details?.resume || "",
    businessDetailsCompanySocialMediaLinkedIn:
      contact.business_details?.companySocialMedia?.linkedin?.url || "",
    businessDetailsCompanySocialMediaTwitter:
      contact.business_details?.companySocialMedia?.twitter?.url || "",
    businessDetailsWorkSchedule: contact.business_details?.workSchedule || "",
    businessDetailsCertifications:
      contact.business_details?.certifications || [],
    businessDetailsIndustry: contact.business_details?.industry || "",
    businessDetailsCompanyInformationCompanyName:
      contact.business_details?.companyInformation?.company_name || "",
    businessDetailsCompanyInformationCompanyLogo:
      contact.business_details?.companyInformation?.company_logo || "",
    businessDetailsCompanyInformationPhone:
      contact.business_details?.companyInformation?.phone || "",
    businessDetailsCompanyInformationEmail:
      contact.business_details?.companyInformation?.email || "",
    businessDetailsCompanyInformationFax:
      contact.business_details?.companyInformation?.fax || "",
    businessDetailsCompanyInformationWebsite:
      contact.business_details?.companyInformation?.website || "",
    businessDetailsCompanyAddressOfficeBuilding:
      contact.business_details?.business_address?.officeBuilding || "",
    businessDetailsCompanyAddressStreet:
      contact.business_details?.business_address?.street || "",
    businessDetailsCompanyAddressCity:
      contact.business_details?.business_address?.city || "",
    businessDetailsCompanyAddressState:
      contact.business_details?.business_address?.state || "",
    businessDetailsCompanyAddressCountry:
      contact.business_details?.business_address?.country || "",
    businessDetailsCompanyAddressPostalCode:
      contact.business_details?.business_address?.postalCode || "",
    businessDetailsBillingAddressOfficeBuilding:
      contact.business_details?.billing_address?.officeBuilding || "",
    businessDetailsBillingAddressStreet:
      contact.business_details?.billing_address?.street || "",
    businessDetailsBillingAddressCity:
      contact.business_details?.billing_address?.city || "",
    businessDetailsBillingAddressState:
      contact.business_details?.billing_address?.state || "",
    businessDetailsBillingAddressCountry:
      contact.business_details?.billing_address?.country || "",
    businessDetailsBillingAddressPostalCode:
      contact.business_details?.billing_address?.postalCode || "",
    businessDetailsCardDetailsNameOnCard:
      contact.business_details?.card_details?.nameOnCard || "",
    businessDetailsCardDetailsCardNumber:
      contact.business_details?.card_details?.cardNumber || "",
    businessDetailsCardDetailsExpiryDate:
      contact.business_details?.card_details?.expiryDate || "",
    businessDetailsAccountDetailsName:
      contact.business_details?.account_details?.name || "",
    businessDetailsAccountDetailsBank:
      contact.business_details?.account_details?.bank || "",
    businessDetailsAccountDetailsAccountNumber:
      contact.business_details?.account_details?.accountNumber || "",
    businessDetailsAccountDetailsIfscCode:
      contact.business_details?.account_details?.ifscCode || "",
    businessDetailsAccountDetailsPaypalEmail:
      contact.business_details?.account_details?.paypalEmail || "",
    businessDetailsCompanySocialMediaFacebook:
      contact.business_details?.companySocialMedia?.facebook?.url || "",
    businessDetailsCompanySocialMediaInstagram:
      contact.business_details?.companySocialMedia?.instagram?.url || "",
    businessDetailsCompanySocialMediaTwitter:
      contact.business_details?.companySocialMedia?.twitter?.url || "",
    businessDetailsCompanySocialMediaLinkedIn:
      contact.business_details?.companySocialMedia?.linkedin?.url || "",
    businessDetailsCompanySocialMediaSnapchat:
      contact.business_details?.companySocialMedia?.snapchat?.url || "",
    businessDetailsCompanySocialMediaSnapchat:
      contact.business_details?.companySocialMedia?.snapchat?.url || "",
    businessDetailsCompanySocialMediaWhatsApp:
      contact.business_details?.companySocialMedia?.whatsapp?.url || "",
    businessDetailsCompanySocialMediaTelegram:
      contact.business_details?.companySocialMedia?.telegram?.url || "",
    businessDetailsCompanySocialMediaSignal:
      contact.business_details?.companySocialMedia?.signal?.url || "",
    businessDetailsCompanySocialMediaSkype:
      contact.business_details?.companySocialMedia?.skype?.url || "",
    businessDetailsCompanySocialMediaYouTube:
      contact.business_details?.companySocialMedia?.youtube?.url || "",
    businessDetailsCompanySocialMediaTwitch:
      contact.business_details?.companySocialMedia?.twitch?.url || "",
    businessDetailsCompanySocialMediaTikTok:
      contact.business_details?.companySocialMedia?.tiktok?.url || "",
    businessDetailsCompanyMessengerIdsIM:
      contact.business_details?.companyMessengerIds?.iMessage?.url || "",
    businessDetailsCompanyMessengerIdsGoogleChat:
      contact.business_details?.companyMessengerIds?.googleChat?.url || "",
    businessDetailsCompanyMessengerIdsDiscord:
      contact.business_details?.companyMessengerIds?.discord?.url || "",
    businessDetailsCompanyMessengerIdsSlack:
      contact.business_details?.companyMessengerIds?.slack?.url || "",
    businessDetailsCompanyMessengerIdsWeChat:
      contact.business_details?.companyMessengerIds?.wechat?.url || "",
    businessDetailsCompanyMessengerIdsKik:
      contact.business_details?.companyMessengerIds?.kik?.url || "",
    businessDetailsCompanyMessengerIdsLine:
      contact.business_details?.companyMessengerIds?.line?.url || "",
    socialMedia: contact.socialMedia || {},
  });
  const [errors, setErrors] = useState({
    emails: [""], // parallel structure to form.emails
    accountDetailsPaypalEmail: "",
    cardDetailsCardNumber: "",
  });

  console.log("Contact:", contact.gender);

  const genderOptions = [
    { label: "Male", value: "male" },
    { label: "Female", value: "female" },
    { label: "Other", value: "other" },
  ];

  const religionOptions = [
    { label: "Hinduism", value: "hinduism" },
    { label: "Islam", value: "islam" },
    { label: "Christianity", value: "christianity" },
    { label: "Buddhism", value: "buddhism" },
    { label: "Sikhism", value: "sikhism" },
    { label: "Judaism", value: "judaism" },
    { label: "Jainism", value: "jainism" },
    { label: "Shinto", value: "shinto" },
    { label: "Taoism", value: "taoism" },
    { label: "Zoroastrianism", value: "zoroastrianism" },
    { label: "Bahá'í Faith", value: "bahai" },
    { label: "Confucianism", value: "confucianism" },
    { label: "Atheism", value: "atheism" },
    { label: "Agnosticism", value: "agnosticism" },
    { label: "Paganism", value: "paganism" },
    { label: "Spiritual but not religious", value: "spiritual" },
    { label: "Other", value: "other" },
  ];

  const timeZoneOptions = [
    { label: "UTC−12:00", value: "Etc/GMT+12" },
    { label: "UTC−11:00", value: "Pacific/Pago_Pago" },
    { label: "UTC−10:00 (Hawaii)", value: "Pacific/Honolulu" },
    { label: "UTC−09:00 (Alaska)", value: "America/Anchorage" },
    { label: "UTC−08:00 (Pacific Time)", value: "America/Los_Angeles" },
    { label: "UTC−07:00 (Mountain Time)", value: "America/Denver" },
    { label: "UTC−06:00 (Central Time)", value: "America/Chicago" },
    { label: "UTC−05:00 (Eastern Time)", value: "America/New_York" },
    { label: "UTC−04:00 (Atlantic Time)", value: "America/Halifax" },
    {
      label: "UTC−03:00 (Buenos Aires)",
      value: "America/Argentina/Buenos_Aires",
    },
    { label: "UTC−02:00", value: "Etc/GMT+2" },
    { label: "UTC−01:00 (Azores)", value: "Atlantic/Azores" },
    { label: "UTC±00:00 (GMT, London)", value: "Europe/London" },
    { label: "UTC+01:00 (Berlin, Paris)", value: "Europe/Berlin" },
    { label: "UTC+02:00 (Cairo, Johannesburg)", value: "Africa/Cairo" },
    { label: "UTC+03:00 (Moscow, Nairobi)", value: "Europe/Moscow" },
    { label: "UTC+03:30 (Tehran)", value: "Asia/Tehran" },
    { label: "UTC+04:00 (Dubai)", value: "Asia/Dubai" },
    { label: "UTC+04:30 (Kabul)", value: "Asia/Kabul" },
    { label: "UTC+05:00 (Islamabad, Tashkent)", value: "Asia/Karachi" },
    { label: "UTC+05:30 (India Standard Time)", value: "Asia/Kolkata" },
    { label: "UTC+05:45 (Nepal)", value: "Asia/Kathmandu" },
    { label: "UTC+06:00 (Dhaka)", value: "Asia/Dhaka" },
    { label: "UTC+06:30 (Myanmar)", value: "Asia/Yangon" },
    { label: "UTC+07:00 (Bangkok, Jakarta)", value: "Asia/Bangkok" },
    { label: "UTC+08:00 (Beijing, Singapore)", value: "Asia/Shanghai" },
    { label: "UTC+09:00 (Tokyo, Seoul)", value: "Asia/Tokyo" },
    { label: "UTC+09:30 (Adelaide)", value: "Australia/Adelaide" },
    { label: "UTC+10:00 (Sydney)", value: "Australia/Sydney" },
    { label: "UTC+11:00", value: "Pacific/Noumea" },
    { label: "UTC+12:00 (Auckland)", value: "Pacific/Auckland" },
    { label: "UTC+13:00", value: "Pacific/Tongatapu" },
    { label: "UTC+14:00", value: "Pacific/Kiritimati" },
  ];
  const cryptoWalletTypeOptions = [
    { label: "Bitcoin (BTC)", value: "bitcoin" },
    { label: "Ethereum (ETH)", value: "ethereum" },
    { label: "Binance Smart Chain (BSC)", value: "bsc" },
    { label: "Solana (SOL)", value: "solana" },
    { label: "Polygon (MATIC)", value: "polygon" },
    { label: "Tron (TRX)", value: "tron" },
    { label: "Ripple (XRP)", value: "ripple" },
    { label: "Cardano (ADA)", value: "cardano" },
    { label: "Litecoin (LTC)", value: "litecoin" },
    { label: "Dogecoin (DOGE)", value: "dogecoin" },
    { label: "Other", value: "other" },
  ];

  const updateForm = (key, value) => {
    // console.log("Updating form key:", key, "with value:", new Date(value));

    setForm((prev) => ({ ...prev, [key]: value }));
  };
  const addPhoneNumber = () => {
    setForm((prev) => ({ ...prev, phones: [...prev.phones, { number: "" }] }));
  };

  const addEmail = () => {
    setForm((prev) => ({ ...prev, emails: [...prev.emails, { address: "" }] }));
  };

  const addAddress = () => {
    setForm((prev) => ({
      ...prev,
      addresses: [...prev.addresses, { street: "" }],
    }));
  };

  const addBusinessDetailsCertification = () => {
    setForm((prev) => ({
      ...prev,
      businessDetailsCertifications: [
        ...prev.businessDetailsCertifications,
        { certification: "" },
      ],
    }));
  };

  const handlePhoneChange = (index, value) => {
    const updatedPhones = [...form.phones];
    updatedPhones[index].number = value;
    updateForm("phones", updatedPhones);
  };

  // const handleEmailChange = (index, value) => {
  //   const updatedEmails = [...form.emails];
  //   updatedEmails[index].address = value;
  //   updateForm("emails", updatedEmails);
  // };

  const handleEmailChange = (index, value) => {
    const updatedEmails = [...form.emails];
    updatedEmails[index].address = value;
    updateForm("emails", updatedEmails);

    const updatedErrors = [...errors.emails];
    updatedErrors[index] = validateEmail(value) ? "" : "Invalid email";
    setErrors({ ...errors, emails: updatedErrors });
  };

  const validateEmail = (email) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
  };

  const handleAddressChange = (index, value) => {
    const updatedAddresses = [...form.addresses];
    updatedAddresses[index].street = value;
    updateForm("addresses", updatedAddresses);
  };

  const handleCertificationChange = (index, value) => {
    const updatedCertifications = [...form.businessDetailsCertifications];
    updatedCertifications[index].certification = value;
    updateForm("businessDetailsCertifications", updatedCertifications);
  };

  const removePhoneNumber = (index) => {
    const updatedPhones = form.phones.filter((_, i) => i !== index);
    updateForm("phones", updatedPhones);
  };

  const removeEmail = (index) => {
    const updatedEmails = form.emails.filter((_, i) => i !== index);
    updateForm("emails", updatedEmails);
  };

  const removeAddress = (index) => {
    const updatedAddresses = form.addresses.filter((_, i) => i !== index);
    updateForm("addresses", updatedAddresses);
  };

  const removeBusinessDetailCertifications = (index) => {
    const updatedCertifications = form.businessDetailsCertifications.filter(
      (_, i) => i !== index
    );
    updateForm("businessDetailsCertifications", updatedCertifications);
  };

  const addBusinessDetails = () => {
    setForm((prev) => ({
      ...prev,
      businessDetails: [
        ...prev.businessDetails,
        { company: "", phone: "", email: "" },
      ],
    }));
  };

  const removeBusinessDetails = (index) => {
    const updatedDetails = form.businessDetails.filter((_, i) => i !== index);
    updateForm("businessDetails", updatedDetails);
  };

  const validate = () => {
    if (!form.firstName.trim()) return "First Name is required";
    if (!form.lastName.trim()) return "Last Name is required";
    if (!form.phones.length) return "At least one phone number is required";
    // form.gender should be male, female, or other only
    const allowedGenders = ["male", "female", "other"];
    const gender = (form.gender || "").trim().toLowerCase();
    if (!allowedGenders.includes(gender))
      return "Gender must be 'male', 'female', or 'other'";

    return "";
  };

  const handleSave = async () => {
    const error = validate();
    if (error) {
      showToast("error", error);
      return;
    }

    // showToast("success", "Contact saved successfully");

    const payload = {
      firstName: form?.firstName,
      middleName: form?.middleName,
      lastName: form?.lastName,
      profile_image: form?.profileImage || "", // Add if in your form state
      company_logo: form?.companyLogo || "", // Add if in your form state
      crypto_wallet: {
        wallet_address: form?.cryptoWalletAddress,
        wallet_type: form?.cryptoWalletType,
      },
      religion: form?.Religion,
      spouseName: form?.spouseName,
      nickname: form?.nickname,
      dateOfBirth: form?.birthday,
      gender: form?.gender.toLowerCase(),
      nationality: form?.nationality,
      maritalStatus: form?.maritalStatus,
      timeZone: form?.timeZone,
      languages: form?.languages.split(",").map((lang) => lang.trim()),
      hobbies: form?.hobbies.split(",").map((hobby) => hobby.trim()),
      emails: form?.emails,
      phoneNumbers: form?.phones,
      addresses: form?.addresses,
      addresses_home: {
        apartment: form?.addressHomeApartment,
        street: form?.addressHomeStreet,
        city: form?.addressHomeCity,
        state: form?.addressHomeState,
        postalCode: form?.addressHomePostalCode,
        country: form?.addressHomeCountry,
      },
      emergency_contact: {
        contactName: form?.emergencyContactName,
        phoneNumber: form?.emergencyContact,
        relationship: form?.emergencyContactRelation,
        // phoneNumber_country_code: "+1", // Or make dynamic
      },
      healthInsurance: {
        policyNumber: form?.healthInsurancePolicyNumber,
        effectiveDate: form?.healthInsuranceEffectiveDate,
        expirationDate: form?.healthInsuranceExpiryDate,
        policyPeriod: 2, // Add dynamic logic if needed
        sumInsured: 100000, // Add dynamic logic if needed
      },
      billing_address: {
        officeBuilding: form?.billingAddressOfficeBuilding,
        street: form?.billingAddressStreet,
        city: form?.billingAddressCity,
        state: form?.billingAddressState,
        postalCode: form?.billingAddressPostalCode,
        country: form?.billingAddressCountry,
      },
      account_details: {
        name: form?.accountDetailsName,
        bank: form?.accountDetailsBank,
        accountNumber: form?.accountDetailsAccountNumber,
        ifscCode: form?.accountDetailsIfscCode,
        paypalEmail: form?.accountDetailsPaypalEmail,
      },
      card_details: {
        nameOnCard: form?.cardDetailsNameOnCard,
        cardNumber: form?.cardDetailsCardNumber,
        expiryDate: form?.cardDetailsExpiryDate,
      },
      socialMedia: form?.socialMedia,
      company: {
        jobTitle: form?.businessDetailsJobTitle,
        department: form?.businessDetailsDepartment,
        workSchedule: form?.businessDetailsWorkSchedule,
        industry: form?.businessDetailsIndustry,
        resume: form?.businessDetailsResume,
        certifications: form?.businessDetailsCertifications,
        workContact: {
          phoneNumber: form?.businessDetailsWorkContactPhoneNumber,
          fax: form?.businessDetailsWorkContactFax,
          email: form?.businessDetailsWorkContactEmail,
        },
        companyInformation: {
          company_name: form?.businessDetailsCompanyInformationCompanyName,
          company_logo: form?.businessDetailsCompanyInformationCompanyLogo,
          phone: form?.businessDetailsCompanyInformationPhone,
          email: form?.businessDetailsCompanyInformationEmail,
          fax: form?.businessDetailsCompanyInformationFax,
          website: form?.businessDetailsCompanyInformationWebsite,
        },
        business_address: {
          officeBuilding: form?.businessDetailsCompanyAddressOfficeBuilding,
          street: form?.businessDetailsCompanyAddressStreet,
          city: form?.businessDetailsCompanyAddressCity,
          state: form?.businessDetailsCompanyAddressState,
          postalCode: form?.businessDetailsCompanyAddressPostalCode,
          country: form?.businessDetailsCompanyAddressCountry,
        },
        billing_address: {
          officeBuilding: form?.businessDetailsBillingAddressOfficeBuilding,
          street: form?.businessDetailsBillingAddressStreet,
          city: form?.businessDetailsBillingAddressCity,
          state: form?.businessDetailsBillingAddressState,
          postalCode: form?.businessDetailsBillingAddressPostalCode,
          country: form?.businessDetailsBillingAddressCountry,
        },
        account_details: {
          name: form?.businessDetailsAccountDetailsName,
          bank: form?.businessDetailsAccountDetailsBank,
          accountNumber: form?.businessDetailsAccountDetailsAccountNumber,
          ifscCode: form?.businessDetailsAccountDetailsIfscCode,
          paypalEmail: form?.businessDetailsAccountDetailsPaypalEmail,
        },
        card_details: {
          nameOnCard: form?.businessDetailsCardDetailsNameOnCard,
          cardNumber: form?.businessDetailsCardDetailsCardNumber,
          expiryDate: form?.businessDetailsCardDetailsExpiryDate,
        },
        companySocialMedia: {
          facebook: { url: form?.businessDetailsCompanySocialMediaFacebook },
          instagram: { url: form?.businessDetailsCompanySocialMediaInstagram },
          twitter: { url: form?.businessDetailsCompanySocialMediaTwitter },
          linkedin: { url: form?.businessDetailsCompanySocialMediaLinkedIn },
          snapchat: { url: form?.businessDetailsCompanySocialMediaSnapchat },
          whatsapp: { url: form?.businessDetailsCompanySocialMediaWhatsApp },
          telegram: { url: form?.businessDetailsCompanySocialMediaTelegram },
          signal: { url: form?.businessDetailsCompanySocialMediaSignal },
          skype: { url: form?.businessDetailsCompanySocialMediaSkype },
          youtube: { url: form?.businessDetailsCompanySocialMediaYouTube },
          twitch: { url: form?.businessDetailsCompanySocialMediaTwitch },
          tiktok: { url: form?.businessDetailsCompanySocialMediaTikTok },
        },
        companyMessengerIds: {
          iMessage: { url: form?.businessDetailsCompanyMessengerIdsIM },
          googleChat: {
            url: form?.businessDetailsCompanyMessengerIdsGoogleChat,
          },
          discord: { url: form?.businessDetailsCompanyMessengerIdsDiscord },
          slack: { url: form?.businessDetailsCompanyMessengerIdsSlack },
          wechat: { url: form?.businessDetailsCompanyMessengerIdsWeChat },
          kik: { url: form?.businessDetailsCompanyMessengerIdsKik },
          line: { url: form?.businessDetailsCompanyMessengerIdsLine },
        },
      },
      notes: form?.notes,
      tagsArray: form?.tagsArray,
    };

    console.log("Payload:", payload, contact._id);

    try {
      setLoading(true);
      const res = await dispatch(
        updateContact({ id: contact._id, data: payload })
      );
      console.log("res:", JSON.stringify(res?.payload?.data));
      if (res?.payload?.success) {
        showToast("success", "Contact updated successfully");
        navigation.goBack();
      } else {
        showToast("error", res.payload.message || "Failed to update contact");
      }
    } catch (error) {
      console.error("Error updating contact:", error);
      showToast("error", "Failed to update contact");
    } finally {
      setLoading(false);
    }
  };

  const validateCardNumber = (number) => {
    const re = /^\d{13,19}$/;
    return re.test(number);
  };


  console.log("form.phones:", form.phones);
  

  return (
    <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
      <ScrollView style={styles.container}>
        {/* back button */}
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{ position: "absolute", top: 20, left: 0 }}
        >
          <Image
            source={icons.backButton}
            style={commonStyles.header.midIcon}
          />
        </TouchableOpacity>

        <View style={{ marginVertical: 20 }} />

        <ProfilePicture
          uri={form.profileImage}
          onUploadSuccess={(url) =>
            setForm((prev) => ({ ...prev, profileImage: url }))
          }
          customStyle={{ height: 150, width: 150, borderRadius: 1000 }}
        />

        <EditableField
          label="First Name"
          placeholder="+Add First Name"
          value={form.firstName}
          onChangeText={(value) => updateForm("firstName", value)}
          maxLength={25}
        />

        {/* Horizontal Line */}
        <View style={styles.horizontalLine} />
        {/* Horizontal Line Ends */}

        <EditableField
          label="Middle Name"
          value={form.middleName}
          onChangeText={(value) => updateForm("middleName", value)}
          maxLength={25}
        />

        {/* Horizontal Line */}
        <View style={styles.horizontalLine} />
        {/* Horizontal Line Ends */}

        <EditableField
          label="Last Name"
          value={form.lastName}
          onChangeText={(value) => updateForm("lastName", value)}
          maxLength={25}
        />

        {/* Horizontal Line */}
        <View style={styles.horizontalLine} />
        {/* Horizontal Line Ends */}

        <TagManager
          contactId={contact._id}
          tagsArray={contact.tagsArray}
          allTags={contact.tagsArray} // or a full list if available
          onChange={(updated) => {
            console.log("Updated Tags Array:", updated);
            setForm((prev) => ({ ...prev, tagsArray: updated }));
          }}
        />

        {/* For Phone */}
        {form.phones.map((phone, index) => (
          <View
            key={index}
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginVertical: 5,
            }}
          >
            <PhoneNumberField
              label={`Phone Number ${index + 1}`}
              value={phone.number}
              onChangeRaw={(value) => handlePhoneChange(index, value)}
              phoneInputRef={React.createRef()} // or manage refs per input if needed
              error={null} // optionally handle errors per input
              setError={() => {}} // if you’re not using error management
              countryCodeProp={phone.callingCode || "QA"}
              callingCodeProp={phone.countryCode || "974"}
              onCountryCodeChange={(newCountryCode) => {
                const updatedPhones = [...form.phones];
                updatedPhones[index].callingCode = newCountryCode;
                updateForm("phones", updatedPhones);
              }}
              onCodeChange={(newCallingCode) => {
                const updatedPhones = [...form.phones];
                updatedPhones[index].countryCode = newCallingCode;
                updateForm("phones", updatedPhones);
              }}
            />

            <TouchableOpacity
              style={{
                position: "absolute",
                right: 0,
                top: 0,
                bottom: 0,
              }}
              onPress={() => removePhoneNumber(index)}
            >
              <Image
                source={icons.minusIconWhite}
                resizeMode="contain"
                style={{ width: 20, height: 20 }}
              />
            </TouchableOpacity>
          </View>
        ))}
        <TouchableOpacity
          style={{
            paddingVertical: 10,
            marginBottom: 10,
          }}
          onPress={addPhoneNumber}
        >
          <MyText bold p>
            + Add Phone Number
          </MyText>
        </TouchableOpacity>
        {/* {form.phones.map((phone, index) => (
          <View
            key={index}
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginVertical: 5,
            }}
          >
            <EditableField
              label={`Phone Number ${index + 1}`}
              value={phone.number}
              onChangeText={(value) => handlePhoneChange(index, value)}
              maxLength={15}
              keyboardType="phone-pad"
            />
            <TouchableOpacity
              style={{
                position: "absolute",
                right: 0,
                top: 0,
                bottom: 0,
              }}
              onPress={() => removePhoneNumber(index)}
            >
              <Image
                source={icons.minusIconWhite}
                resizeMode="contain"
                style={{ width: 20, height: 20 }}
              />
            </TouchableOpacity>
          </View>
        ))} */}
        {/* <TouchableOpacity
          style={{
            paddingVertical: 10,
            marginBottom: 10,
          }}
          onPress={addPhoneNumber}
        >
          <MyText bold p>
            + Add Phone Number
          </MyText>
        </TouchableOpacity> */}

        {/* Horizontal Line */}
        <View style={styles.horizontalLine} />
        {/* Horizontal Line Ends */}

        {/* FOR Email */}
        {form.emails.map((email, index) => (
          <View
            key={index}
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginVertical: 5,
            }}
          >
            <EditableField
              label={`Email ${index + 1}`}
              value={email.address}
              onChangeText={(value) => handleEmailChange(index, value)}
              error={errors.emails[index]} // Pass the error message
            />
            <TouchableOpacity
              style={{
                position: "absolute",
                right: 10,
                top: 0,
                bottom: 0,
              }}
              onPress={() => removeEmail(index)}
            >
              <Image
                source={icons.minusIconWhite}
                resizeMode="contain"
                style={{ width: 20, height: 20 }}
              />
            </TouchableOpacity>
          </View>
        ))}
        <TouchableOpacity
          style={{ paddingVertical: 10, marginBottom: 10 }}
          onPress={addEmail}
        >
          <MyText bold p>
            + Add Email
          </MyText>
        </TouchableOpacity>

        {/* Horizontal Line */}
        <View style={styles.horizontalLine} />
        {/* Horizontal Line Ends */}

        {/* <EditableField
          label="Address"
          value={form.address}
          onChangeText={(value) => updateForm("address", value)}
        /> */}

        <DropdownSection title="Personal Details">
          <MyText h5 bold style={{ marginBottom: 10 }}>
            • Basic Details
          </MyText>
          {/* <EditableField
            label="Gender"
            value={form.gender}
            onChangeText={(value) => updateForm("gender", value)}
          /> */}
          <CustomDropDown
            label="Gender"
            data={genderOptions}
            defaultValue={form.gender}
            onSelect={(item) => updateForm("gender", item.value)}
            customStyles={{
              // borderBottomWidth: 1,
            }}
            //height={38}
            marginBottom={0}
            labelStyle={{ letterSpacing: 0.2, fontFamily: "Metropolis-Bold" }}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <MyText bold h6 style={{ marginBottom: 10 }}>
            Official Address
          </MyText>
          <View style={{ gap: 10 }}>
            <EditableField
              label="Apartment"
              value={form.addressHomeApartment}
              onChangeText={(value) =>
                updateForm("addressHomeApartment", value)
              }
            />
            <EditableField
              label="Street"
              value={form.addressHomeStreet}
              onChangeText={(value) => updateForm("addressHomeStreet", value)}
            />
            <EditableField
              label="City"
              value={form.addressHomeCity}
              onChangeText={(value) => updateForm("addressHomeCity", value)}
            />
            <EditableField
              label="State"
              value={form.addressHomeState}
              onChangeText={(value) => updateForm("addressHomeState", value)}
            />
            <EditableField
              label="Country"
              value={form.addressHomeCountry}
              onChangeText={(value) => updateForm("addressHomeCountry", value)}
            />
            <EditableField
              label="Postal Code"
              value={form.addressHomePostalCode}
              onChangeText={(value) =>
                updateForm("addressHomePostalCode", value)
              }
              keyboardType={"phone-pad"}
              maxLength={10}
            />
          </View>
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          {form.addresses.map((address, index) => (
            <View key={index} style={{ marginBottom: 16, gap: 10 }}>
              <MyText bold h6 style={{ marginBottom: 10 }}>
                Other Address {index + 1}
              </MyText>
              <EditableField
                label={`Apartment`}
                value={address.apartment}
                onChangeText={(value) =>
                  handleAddressChange(index, "apartment", value)
                }
              />
              <EditableField
                label={`Street`}
                value={address.street}
                onChangeText={(value) =>
                  handleAddressChange(index, "street", value)
                }
              />
              <EditableField
                label={`City`}
                value={address.city}
                onChangeText={(value) =>
                  handleAddressChange(index, "city", value)
                }
              />
              <EditableField
                label={`State`}
                value={address.state}
                onChangeText={(value) =>
                  handleAddressChange(index, "state", value)
                }
              />
              <EditableField
                label={`Country`}
                value={address.country}
                onChangeText={(value) =>
                  handleAddressChange(index, "country", value)
                }
              />
              <EditableField
                label={`Postal Code`}
                value={address.postalCode}
                onChangeText={(value) =>
                  handleAddressChange(index, "postalCode", value)
                }
              />

              <TouchableOpacity
                style={{ position: "absolute", right: 0, top: 0, bottom: 0 }}
                onPress={() => removeAddress(index)}
              >
                <Image
                  source={icons.minusIconWhite}
                  resizeMode="contain"
                  style={{ width: 20, height: 20 }}
                />
              </TouchableOpacity>
            </View>
          ))}

          <TouchableOpacity
            style={{ paddingVertical: 10, marginBottom: 10 }}
            onPress={addAddress}
          >
            <MyText bold p>
              + Add Address
            </MyText>
          </TouchableOpacity>

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          {/* <EditableField
            label="Birthday"
            value={form.birthday}
            onChangeText={(value) => updateForm("birthday", value)}
          /> */}
          <EditableField
            label={"Notes"}
            value={form.notes}
            onChangeText={(value) => updateForm("notes", value)}
            multiline={true}
            placeholder="Add notes here"
            containerStyle={{ height: 100 }}
            style={{ textAlignVertical: "top" }}
            maxLength={500}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <DatePicker
            label="Birthday"
            value={form.birthday}
            onChangeDate={(date) => updateForm("birthday", date)}
            placeholder="Select your birthday"
            containerStyle={{ paddingVertical: 0 }}
            labelStyle={{ letterSpacing: 0.2, fontFamily: "Metropolis-Bold" }}
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Nationality"
            value={form.nationality}
            onChangeText={(value) => updateForm("nationality", value)}
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <MyText h5 bold style={{ marginBottom: 10 }}>
            • Other Details
          </MyText>
          <EditableField
            label="Marital Status"
            value={form.maritalStatus}
            onChangeText={(value) => updateForm("maritalStatus", value)}
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Nickname"
            value={form.nickname}
            onChangeText={(value) => updateForm("nickname", value)}
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          {/* spouse name */}
          <EditableField
            label="Spouse Name"
            value={form.spouseName}
            onChangeText={(value) => updateForm("spouseName", value)}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Languages"
            value={form.languages}
            onChangeText={(value) => updateForm("languages", value)}
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Hobbies"
            value={form.hobbies}
            onChangeText={(value) => updateForm("hobbies", value)}
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          {/* religion */}

          {/* <EditableField
            label="Religion"
            value={form.Religion}
            onChangeText={(value) => updateForm("Religion", value)}
          /> */}
          <CustomDropDown
            label="Religion"
               //height={38}
            data={religionOptions}
            defaultValue={form.Religion}
            onSelect={(item) => updateForm("Religion", item.value)}
                 customStyles={{
              // borderBottomWidth: 1,
            }}
            marginBottom={0}
            labelStyle={{ letterSpacing: 0.2, fontFamily: "Metropolis-Bold" }}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          {/* timeZone */}
          {/* <EditableField
            label="Time Zone"
            value={form.timeZone}
            onChangeText={(value) => updateForm("timeZone", value)}
          /> */}
          <CustomDropDown
            label="Time Zone"
               //height={38}
            data={timeZoneOptions}
            defaultValue={form.timeZone}
            onSelect={(item) => updateForm("timeZone", item.value)}
                 customStyles={{
              // borderBottomWidth: 1,
            }}
            marginBottom={0}
            labelStyle={{ letterSpacing: 0.2, fontFamily: "Metropolis-Bold" }}
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Emergency Contact"
            value={form.emergencyContact}
            onChangeText={(value) => updateForm("emergencyContact", value)}
            keyboardType={"phone-pad"}
            maxLength={15}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Emergency Contact Name"
            value={form.emergencyContactName}
            onChangeText={(value) => updateForm("emergencyContactName", value)}
            maxLength={50}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}
          <EditableField
            label="Emergency Contact Relation"
            value={form.emergencyContactRelation}
            onChangeText={(value) =>
              updateForm("emergencyContactRelation", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Health Insurance Policy Number"
            value={form.healthInsurancePolicyNumber}
            onChangeText={(value) =>
              updateForm("healthInsurancePolicyNumber", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <DatePicker
            label="Health Insurance Expiry Date"
            value={form.healthInsuranceExpiryDate}
            onChangeDate={(date) =>
              updateForm("healthInsuranceExpiryDate", date)
            }
            placeholder="Select expiry date"
            // containerStyle={{ backgroundColor: "#FFFDF6" }}
            labelStyle={{ letterSpacing: 0.2, fontFamily: "Metropolis-Bold" }}
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <DatePicker
            label="Health Insurance Effective Date"
            value={form.healthInsuranceEffectiveDate}
            onChangeDate={(date) =>
              updateForm("healthInsuranceEffectiveDate", date)
            }
            placeholder="Select effective date"
            // containerStyle={{ backgroundColor: "#FFFDF6" }}
            labelStyle={{ letterSpacing: 0.2, fontFamily: "Metropolis-Bold" }}
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Billing Address Office Building"
            value={form.billingAddressOfficeBuilding}
            onChangeText={(value) =>
              updateForm("billingAddressOfficeBuilding", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Billing Address Street"
            value={form.billingAddressStreet}
            onChangeText={(value) => updateForm("billingAddressStreet", value)}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Billing Address City"
            value={form.billingAddressCity}
            onChangeText={(value) => updateForm("billingAddressCity", value)}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Billing Address State"
            value={form.billingAddressState}
            onChangeText={(value) => updateForm("billingAddressState", value)}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Billing Address Country"
            value={form.billingAddressCountry}
            onChangeText={(value) => updateForm("billingAddressCountry", value)}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Billing Address Postal Code"
            value={form.billingAddressPostalCode}
            onChangeText={(value) =>
              updateForm("billingAddressPostalCode", value)
            }
            keyboardType={"phone-pad"}
            maxLength={10}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Account Details Name"
            value={form.accountDetailsName}
            onChangeText={(value) => updateForm("accountDetailsName", value)}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Account Details Bank"
            value={form.accountDetailsBank}
            onChangeText={(value) => updateForm("accountDetailsBank", value)}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Account Details Account Number"
            value={form.accountDetailsAccountNumber}
            onChangeText={(value) =>
              updateForm("accountDetailsAccountNumber", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Account Details IFSC Code"
            value={form.accountDetailsIfscCode}
            onChangeText={(value) =>
              updateForm("accountDetailsIfscCode", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Account Details Paypal Email"
            value={form.accountDetailsPaypalEmail}
            onChangeText={(value) => {
              updateForm("accountDetailsPaypalEmail", value);

              // Live validation (optional)
              if (!validateEmail(value)) {
                setErrors((prev) => ({
                  ...prev,
                  accountDetailsPaypalEmail: "Invalid email format",
                }));
              } else {
                setErrors((prev) => ({
                  ...prev,
                  accountDetailsPaypalEmail: "",
                }));
              }
            }}
            error={errors.accountDetailsPaypalEmail}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Card Details Name On Card"
            value={form.cardDetailsNameOnCard}
            onChangeText={(value) => updateForm("cardDetailsNameOnCard", value)}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Card Details Card Number"
            value={form.cardDetailsCardNumber}
            onChangeText={(value) => {
              updateForm("cardDetailsCardNumber", value);

              // Basic live validation
              if (!validateCardNumber(value)) {
                setErrors((prev) => ({
                  ...prev,
                  cardDetailsCardNumber: "Card number must be 13–19 digits",
                }));
              } else {
                setErrors((prev) => ({
                  ...prev,
                  cardDetailsCardNumber: "",
                }));
              }
            }}
            error={errors.cardDetailsCardNumber}
            maxLength={19}
            keyboardType="numeric"
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <DatePicker
            label="Card Details Expiry Date"
            value={form.cardDetailsExpiryDate}
            onChangeDate={(date) => updateForm("cardDetailsExpiryDate", date)}
            placeholder="MM / YYYY"
            // containerStyle={{ backgroundColor: "#FFFDF6" }}
            labelStyle={{ letterSpacing: 0.2, fontFamily: "Metropolis-Bold" }}
            // mode="date"
            // onlyMonthYear={true} // Custom prop to handle only month and year
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Crypto Wallet Address"
            value={form.cryptoWalletAddress}
            onChangeText={(value) => updateForm("cryptoWalletAddress", value)}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <CustomDropDown
            label="Crypto Wallet Type"
               //height={38}
            data={cryptoWalletTypeOptions}
            defaultValue={form.cryptoWalletType}
            onSelect={(item) => updateForm("cryptoWalletType", item.value)}
                 customStyles={{
              // borderBottomWidth: 1,
            }}
            marginBottom={0}
            labelStyle={{ letterSpacing: 0.2, fontFamily: "Metropolis-Bold" }}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}
        </DropdownSection>

        {/* MULTIPLE BUSINESS CARDS FOR FUTURE */}

        {/* <DropdownSection title="Business Details">
                  {form.businessDetails.map((detail, index) => (
          <View key={index} style={{ marginVertical: 10 }}>
            <MyText bold p>
              Business Detail {index + 1}
            </MyText>
              <EditableField
                label="Company"
                value={detail.company}
                onChangeText={(value) => updateForm("businessDetails", form.businessDetails.map((d, i) => i === index ? { ...d, company: value } : d))}
              />
              <EditableField
                label="Company Phone"
                value={detail.phone}
                onChangeText={(value) => updateForm("businessDetails", form.businessDetails.map((d, i) => i === index ? { ...d, phone: value } : d))}
              />
              <EditableField
                label="Company Email"
                value={detail.email}
                onChangeText={(value) => updateForm("businessDetails", form.businessDetails.map((d, i) => i === index ? { ...d, email: value } : d))}
              />
              <TouchableOpacity onPress={() => removeBusinessDetails(index)}>
                <MyText>Remove Business Detail</MyText>
              </TouchableOpacity>
            
          </View>
        ))}
        <TouchableOpacity onPress={addBusinessDetails}>
          <MyText>+ Add Business Detail</MyText>
        </TouchableOpacity>
        </DropdownSection> */}

        <DropdownSection title="Business Details">
          <MyText h5 bold style={{ marginBottom: 10 }}>
            • Employee Info
          </MyText>

          <EditableField
            label="Job Title"
            value={form.businessDetailsJobTitle}
            onChangeText={(value) =>
              updateForm("businessDetailsJobTitle", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Name"
            value={form.businessDetailsCompanyName}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyName", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Department"
            value={form.businessDetailsDepartment}
            onChangeText={(value) =>
              updateForm("businessDetailsDepartment", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Work Phone Number"
            value={form.businessDetailsWorkContactPhoneNumber}
            onChangeText={(value) =>
              updateForm("businessDetailsWorkContactPhoneNumber", value)
            }
            keyboardType={"phone-pad"}
            maxLength={15}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Work Email"
            value={form.businessDetailsWorkContactEmail}
            onChangeText={(value) =>
              updateForm("businessDetailsWorkContactEmail", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Work Fax Number"
            value={form.businessDetailsWorkContactFax}
            onChangeText={(value) =>
              updateForm("businessDetailsWorkContactFax", value)
            }
            keyboardType={"phone-pad"}
            maxLength={15}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Resume"
            value={form.businessDetailsResume}
            onChangeText={(value) => updateForm("businessDetailsResume", value)}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="LinkedIn Profile"
            value={form.businessDetailsCompanySocialMediaLinkedIn}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaLinkedIn", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Twitter Handle"
            value={form.businessDetailsCompanySocialMediaTwitter}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaTwitter", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Work Schedule/Hours"
            value={form.businessDetailsWorkSchedule}
            onChangeText={(value) =>
              updateForm("businessDetailsWorkSchedule", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          {/* certfications */}
          {/* {contact.business_details?.certifications?.map(
                          (certification, index) =>
                            renderDetailItem(
                              `Professional Certification ${index + 1}`,
                              certification.url
                            )
                        )} */}
          {form.businessDetailsCertifications.map((certification, index) => (
            <View key={index} style={{ marginBottom: 16 }}>
              <MyText bold h6 style={{ marginBottom: 10 }}>
                Professional Certification {index + 1}
              </MyText>
              <EditableField
                label={`Certification`}
                value={certification?.url}
                onChangeText={(value) =>
                  handleCertificationChange(index, value)
                }
              />
              <TouchableOpacity
                style={{ position: "absolute", right: 0, top: 0, bottom: 0 }}
                onPress={() => removeBusinessDetailCertifications(index)}
              >
                <Image
                  source={icons.minusIconWhite}
                  resizeMode="contain"
                  style={{ width: 20, height: 20 }}
                />
              </TouchableOpacity>
            </View>
          ))}
          <TouchableOpacity
            style={{ paddingVertical: 10, marginBottom: 10 }}
            onPress={addBusinessDetailsCertification}
          >
            <MyText bold p>
              + Add Professional Certification
            </MyText>
          </TouchableOpacity>

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          {/* industry */}
          <EditableField
            label="Industry"
            value={form.businessDetailsIndustry}
            onChangeText={(value) =>
              updateForm("businessDetailsIndustry", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <MyText h5 bold style={{ marginBottom: 10 }}>
            • Company Info
          </MyText>

          <EditableField
            label="Company Name"
            value={form.businessDetailsCompanyInformationCompanyName}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyInformationCompanyName", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company logo"
            value={form.businessDetailsCompanyInformationCompanyLogo}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyInformationCompanyLogo", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Phone Number"
            value={form.businessDetailsCompanyInformationPhone}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyInformationPhone", value)
            }
            keyboardType={"phone-pad"}
            maxLength={15}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Email"
            value={form.businessDetailsCompanyInformationEmail}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyInformationEmail", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Fax Number"
            value={form.businessDetailsCompanyInformationFax}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyInformationFax", value)
            }
            keyboardType={"phone-pad"}
            maxLength={15}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Website"
            value={form.businessDetailsCompanyInformationWebsite}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyInformationWebsite", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Address Office Building"
            value={form.businessDetailsCompanyAddressOfficeBuilding}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyAddressOfficeBuilding", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Address Street"
            value={form.businessDetailsCompanyAddressStreet}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyAddressStreet", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Address City"
            value={form.businessDetailsCompanyAddressCity}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyAddressCity", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Address State"
            value={form.businessDetailsCompanyAddressState}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyAddressState", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Address Country"
            value={form.businessDetailsCompanyAddressCountry}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyAddressCountry", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Address Postal Code"
            value={form.businessDetailsCompanyAddressPostalCode}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyAddressPostalCode", value)
            }
            keyboardType={"phone-pad"}
            maxLength={10}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Billing Address Office Building"
            value={form.businessDetailsBillingAddressOfficeBuilding}
            onChangeText={(value) =>
              updateForm("businessDetailsBillingAddressOfficeBuilding", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Billing Address Street"
            value={form.businessDetailsBillingAddressStreet}
            onChangeText={(value) =>
              updateForm("businessDetailsBillingAddressStreet", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Billing Address City"
            value={form.businessDetailsBillingAddressCity}
            onChangeText={(value) =>
              updateForm("businessDetailsBillingAddressCity", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Billing Address State"
            value={form.businessDetailsBillingAddressState}
            onChangeText={(value) =>
              updateForm("businessDetailsBillingAddressState", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Billing Address Country"
            value={form.businessDetailsBillingAddressCountry}
            onChangeText={(value) =>
              updateForm("businessDetailsBillingAddressCountry", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Billing Address Postal Code"
            value={form.businessDetailsBillingAddressPostalCode}
            onChangeText={(value) =>
              updateForm("businessDetailsBillingAddressPostalCode", value)
            }
            keyboardType={"phone-pad"}
            maxLength={10}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Account Details Name"
            value={form.businessDetailsAccountDetailsName}
            onChangeText={(value) =>
              updateForm("businessDetailsAccountDetailsName", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Account Details Bank"
            value={form.businessDetailsAccountDetailsBank}
            onChangeText={(value) =>
              updateForm("businessDetailsAccountDetailsBank", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Account Details Account Number"
            value={form.businessDetailsAccountDetailsAccountNumber}
            onChangeText={(value) =>
              updateForm("businessDetailsAccountDetailsAccountNumber", value)
            }
            keyboardType={"phone-pad"}
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Account Details IFSC Code"
            value={form.businessDetailsAccountDetailsIfscCode}
            onChangeText={(value) =>
              updateForm("businessDetailsAccountDetailsIfscCode", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Account Details Paypal Email"
            value={form.businessDetailsAccountDetailsPaypalEmail}
            onChangeText={(value) =>
              updateForm("businessDetailsAccountDetailsPaypalEmail", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Social Media Facebook"
            value={form.businessDetailsCompanySocialMediaFacebook}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaFacebook", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Social Media Instagram"
            value={form.businessDetailsCompanySocialMediaInstagram}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaInstagram", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Social Media Twitter"
            value={form.businessDetailsCompanySocialMediaTwitter}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaTwitter", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Social Media LinkedIn"
            value={form.businessDetailsCompanySocialMediaLinkedIn}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaLinkedIn", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Social Media Snapchat"
            value={form.businessDetailsCompanySocialMediaSnapchat}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaSnapchat", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Social Media WhatsApp"
            value={form.businessDetailsCompanySocialMediaWhatsApp}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaWhatsApp", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Social Media Telegram"
            value={form.businessDetailsCompanySocialMediaTelegram}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaTelegram", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Social Media Signal"
            value={form.businessDetailsCompanySocialMediaSignal}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaSignal", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Social Media Skype"
            value={form.businessDetailsCompanySocialMediaSkype}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaSkype", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Social Media YouTube"
            value={form.businessDetailsCompanySocialMediaYouTube}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaYouTube", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Social Media Twitch"
            value={form.businessDetailsCompanySocialMediaTwitch}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaTwitch", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Social Media TikTok"
            value={form.businessDetailsCompanySocialMediaTikTok}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanySocialMediaTikTok", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Messenger IDs iMessage"
            value={form.businessDetailsCompanyMessengerIdsIM}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyMessengerIdsIM", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Messenger IDs Google Chat"
            value={form.businessDetailsCompanyMessengerIdsGoogleChat}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyMessengerIdsGoogleChat", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Messenger IDs Discord"
            value={form.businessDetailsCompanyMessengerIdsDiscord}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyMessengerIdsDiscord", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Messenger IDs Slack"
            value={form.businessDetailsCompanyMessengerIdsSlack}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyMessengerIdsSlack", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Messenger IDs WeChat"
            value={form.businessDetailsCompanyMessengerIdsWeChat}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyMessengerIdsWeChat", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Messenger IDs Kik"
            value={form.businessDetailsCompanyMessengerIdsKik}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyMessengerIdsKik", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Company Messenger IDs Line"
            value={form.businessDetailsCompanyMessengerIdsLine}
            onChangeText={(value) =>
              updateForm("businessDetailsCompanyMessengerIdsLine", value)
            }
          />
          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}
        </DropdownSection>

        <DropdownSection title="Social Details">
          <EditableField
            label="Facebook"
            value={form.socialMedia?.facebook?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                facebook: { ...form.socialMedia.facebook, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Instagram"
            value={form.socialMedia?.instagram?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                instagram: { ...form.socialMedia.instagram, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Twitter"
            value={form.socialMedia?.twitter?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                twitter: { ...form.socialMedia.twitter, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="LinkedIn"
            value={form.socialMedia?.linkedin?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                linkedin: { ...form.socialMedia.linkedin, url: value },
              })
            }
          />
          <EditableField
            label="Snapchat"
            value={form.socialMedia?.snapchat?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                snapchat: { ...form.socialMedia.snapchat, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="TikTok"
            value={form.socialMedia?.tiktok?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                tiktok: { ...form.socialMedia.tiktok, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="YouTube"
            value={form.socialMedia?.youtube?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                youtube: { ...form.socialMedia.youtube, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="WhatsApp"
            value={form.socialMedia?.whatsapp?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                whatsapp: { ...form.socialMedia.whatsapp, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Telegram"
            value={form.socialMedia?.telegram?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                telegram: { ...form.socialMedia.telegram, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Signal"
            value={form.socialMedia?.signal?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                signal: { ...form.socialMedia.signal, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="WeChat"
            value={form.socialMedia?.wechat?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                wechat: { ...form.socialMedia.wechat, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Viber"
            value={form.socialMedia?.viber?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                viber: { ...form.socialMedia.viber, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Skype"
            value={form.socialMedia?.skype?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                skype: { ...form.socialMedia.skype, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Pinterest"
            value={form.socialMedia?.pinterest?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                pinterest: { ...form.socialMedia.pinterest, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Tumblr"
            value={form.socialMedia?.tumblr?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                tumblr: { ...form.socialMedia.tumblr, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Flickr"
            value={form.socialMedia?.flickr?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                flickr: { ...form.socialMedia.flickr, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Reddit"
            value={form.socialMedia?.reddit?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                reddit: { ...form.socialMedia.reddit, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Quora"
            value={form.socialMedia?.quora?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                quora: { ...form.socialMedia.quora, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}

          <EditableField
            label="Discord"
            value={form.socialMedia?.discord?.url}
            onChangeText={(value) =>
              updateForm("socialMedia", {
                ...form.socialMedia,
                discord: { ...form.socialMedia.discord, url: value },
              })
            }
          />

          {/* Horizontal Line */}
          <View style={styles.horizontalLine} />
          {/* Horizontal Line Ends */}
        </DropdownSection>

        {/* Delete button */}
        <TouchableOpacity
          onPress={() => {
            Alert.alert(
              "Delete Contact",
              "Are you sure you want to delete this contact?",
              [
                { text: "Cancel", style: "cancel" },
                {
                  text: "Delete",
                  onPress: () => {
                    // Handle delete contact logic here
                    showToast("success", "Contact deleted successfully");
                    navigation.goBack();
                  },
                },
              ]
            );
          }}
          style={[
            commonStyles.rowWithoutSpaceBetween,
            { alignItems: "center", marginTop: 10 },
          ]}
        >
          <Image
            source={icons.deleteAccountIcon}
            resizeMode="contain"
            style={{ width: 20, height: 20, tintColor: "red" }}
          />
          <MyText regular color="red" style={{ marginTop: 5 }}>
            Delete Contact
          </MyText>
        </TouchableOpacity>

        {/* Save button */}
        <View style={{ marginVertical: 20 }}>
          <PrimaryButton title={"Save Changes"} onPress={handleSave} />
        </View>
      </ScrollView>
      <AppLoader isLoading={loading} />
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20, backgroundColor: "#fff" },
  horizontalLine: {
    borderBottomWidth: 1,
    borderBottomColor: "#ccc",
    marginVertical: 16,
  },
});

export default EditContactScreen;
