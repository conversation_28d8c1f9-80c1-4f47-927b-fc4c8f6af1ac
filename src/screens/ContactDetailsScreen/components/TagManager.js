import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  ScrollView,
} from "react-native";
import MyText from "../../../components/MyText";
import { PrimaryButton } from "../../../components/Button";

const TagManager = ({ contactId, tagsArray, allTags, onChange }) => {
  const [modalVisible, setModalVisible] = useState(false);

  const selectedTagIds = tagsArray
    .filter((tag) => tag.addInTag.some((m) => m.memberId === contactId))
    .map((tag) => tag.tagId);

  const [activeTagIds, setActiveTagIds] = useState(selectedTagIds);

  const toggleTag = (tagId) => {
    setActiveTagIds((prev) =>
      prev.includes(tagId)
        ? prev.filter((id) => id !== tagId)
        : [...prev, tagId]
    );
  };

  const saveTags = () => {
    const updated = allTags.map((tag) => {
      const isActive = activeTagIds.includes(tag.tagId);
      const hasMember = tag.addInTag.some((m) => m.memberId === contactId);

      return {
        ...tag,
        addInTag: isActive
          ? hasMember
            ? tag.addInTag
            : [...tag.addInTag, { memberId: contactId }]
          : tag.addInTag.filter((m) => m.memberId !== contactId),
      };
    });

    onChange(updated);
    setModalVisible(false);
  };

  const getTagName = (tagId) => {
    return allTags.find((tag) => tag.tagId === tagId)?.tag_name || "";
  };

  return (
    <View>
      <View style={styles.header}>
        <MyText p bold style={styles.label}>
          Tags
        </MyText>
        <TouchableOpacity onPress={() => setModalVisible(true)}>
          <Text style={styles.editLink}>✎ Edit/Add Tag</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.tagsWrapper}>
        {activeTagIds.length === 0 && (
          <MyText p style={styles.tagText}>
            No tags added
          </MyText>
        )}
        {activeTagIds.length > 0 &&
          activeTagIds.map((tagId) => (
            <MyText key={tagId} style={styles.tagText}>
              {getTagName(tagId)}
            </MyText>
          ))}
      </View>

      <Modal visible={modalVisible} animationType="slide">
        <View style={styles.modalContainer}>
          <MyText style={styles.modalTitle}>Manage Tags</MyText>

          <ScrollView contentContainerStyle={styles.chipScroll}>
            {allTags.map((tag) => {
              const isSelected = activeTagIds.includes(tag.tagId);
              return (
                <TouchableOpacity
                  key={tag.tagId}
                  onPress={() => toggleTag(tag.tagId)}
                  style={[styles.chip, isSelected && styles.chipSelected]}
                >
                  <MyText
                    style={
                      isSelected ? styles.chipTextSelected : styles.chipText
                    }
                  >
                    {tag.tag_name}
                    {isSelected ? "  ✕" : ""}
                  </MyText>
                </TouchableOpacity>
              );
            })}
          </ScrollView>

          {/* <TouchableOpacity onPress={saveTags} style={styles.saveButton}>
            <Text style={styles.saveButtonText}>Save</Text>
          </TouchableOpacity> */}
          <PrimaryButton title="Save" onPress={saveTags} />

          <TouchableOpacity
            onPress={() => setModalVisible(false)}
            style={styles.cancelButton}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 10,
  },
  label: { marginBottom: 4, textTransform: "capitalize" },
  editLink: { color: "#3366FF", fontWeight: "600" },
  tagsWrapper: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 6,
    marginBottom: 10,
  },
  tagText: {
    backgroundColor: "#eee",
    padding: 6,
    borderRadius: 12,
    marginRight: 6,
    marginBottom: 6,
  },
  modalContainer: { flex: 1, padding: 20, backgroundColor: "white" },
  modalTitle: { fontSize: 18, fontWeight: "bold", marginBottom: 12 },
  chipScroll: { flexDirection: "row", flexWrap: "wrap" },
  chip: {
    borderColor: "#ccc",
    borderWidth: 1,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    margin: 6,
  },
  chipSelected: {
    backgroundColor: "#3366FF",
    borderColor: "#3366FF",
  },
  chipText: { color: "#000" },
  chipTextSelected: { color: "#fff" },
  saveButton: {
    marginTop: 20,
    backgroundColor: "#3366FF",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  saveButtonText: { color: "white", fontWeight: "bold" },
  cancelButton: {
    marginTop: 10,
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  cancelButtonText: { color: "#666" },
});

export default TagManager;
