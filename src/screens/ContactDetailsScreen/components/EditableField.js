import React from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import MyText from "../../../components/MyText";
import colors from "../../../assets/colors";

const EditableField = ({
  label,
  value,
  onChangeText,
  onRemove,
  removable,
  multiline = false,
  placeholder,
  error,
  maxLength,
  keyboardType,
}) => {
  return (
    <View style={styles.container}>
      {label && (
        <MyText p bold style={styles.label}>
          {label}
        </MyText>
      )}
      <View style={styles.inputWrapper}>
        <TextInput
          value={value}
          onChangeText={onChangeText}
          multiline={multiline}
          style={[
            styles.input,
            multiline && { height: 80, textAlignVertical: "top" },
            error && { borderColor: "red", borderBottomWidth: 1 },
          ]}
          placeholder={`+Add ${label}`}
          placeholderTextColor={colors.black}
          maxLength={maxLength}
          keyboardType={keyboardType}
        />
        {removable && (
          <TouchableOpacity onPress={onRemove} style={styles.removeButton}>
            <Text style={styles.removeText}>-</Text>
          </TouchableOpacity>
        )}
      </View>
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { width: "100%" },
  label: { marginBottom: 4, textTransform: "capitalize" },
  inputWrapper: { flexDirection: "row", alignItems: "center", },
  input: {
    flex: 1,
    padding: 10,
    backgroundColor: "#f2f2f2",
    // borderBottomWidth: 0.5,
    // borderColor: '#ccc',
    borderRadius: 10,
    textTransform: "capitalize",
    color: colors.black,
    height: 48,
  },
  removeButton: { marginLeft: 10, },
  removeText: { color: "red", fontSize: 20, textAlign: "center" },
  errorText: {
    color: "red",
    marginTop: 4,
    fontSize: 12,
  },
});

export default EditableField;
