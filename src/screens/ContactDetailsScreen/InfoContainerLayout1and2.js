import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from "react-native";
import colors from "../../assets/colors";
import icons from "../../assets/icons";
import MyText from "../../components/MyText";
import { useState } from "react";
import { formatDate } from "../../utils/commonHelpers";

const InfoContainerLayout1and2 = ({
  renderDetailItem,
  contact,
  toggleFavorite,
  isFavorite,
  personal,
}) => {
  const [expandedSections, setExpandedSections] = useState([]);
  const toggleSection = (section) => {
    setExpandedSections((prev) =>
      prev.includes(section)
        ? prev.filter((item) => item !== section)
        : [...prev, section]
    );
  };

  const officialAddressParts = [
    contact?.addresses_home?.apartment,
    contact?.addresses_home?.street,
    contact?.addresses_home?.city,
    contact?.addresses_home?.state,
    contact?.addresses_home?.country,
    contact?.addresses_home?.postalCode,
  ];
  const formattedAddress = officialAddressParts.filter(Boolean).join(", ");

  const otherAddressParts = [
    contact?.addresses_other?.apartment,
    contact?.addresses_other?.street,
    contact?.addresses_other?.city,
    contact?.addresses_other?.state,
    contact?.addresses_other?.country,
    contact?.addresses_other?.postalCode,
  ];

  const capitalize = (str) =>
    str.charAt(0).toUpperCase() + str.slice(1).replace(/([A-Z])/g, " $1");

  return (
    <View style={styles.infoContainer}>
      <View>
        {/* Favorite button */}
        {!personal && (
          <TouchableOpacity
            onPress={toggleFavorite}
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 10,
              // backgroundColor: "pink",
              position: "absolute",
              right: 0,
              top: 0,
              // bottom: 0,
              zIndex: 1,
            }}
          >
            <Image
              source={isFavorite ? icons.heartIconFilled : icons.heartIcon}
              style={{ width: 16, height: 16, marginRight: 10 }}
            />
            <MyText p bold>
              {isFavorite ? "Remove from Favorites" : "Add to Favorites"}
            </MyText>
          </TouchableOpacity>
        )}
        {contact?.phoneNumbers?.map((phone, index) => (
          <View key={index}>
            {renderDetailItem(
              `Mobile ${index + 1}`,
              phone?.number
                ? phone.countryCode
                  ? `${
                      phone.countryCode.startsWith("+")
                        ? phone.countryCode
                        : `+${phone.countryCode}`
                    } ${phone.number}`
                  : phone.number
                : "No phone number available"
            )}
          </View>
        ))}
      </View>

      {/* {renderDetailItem("Email", contact?.emails?.[0]?.address)} */}
      {contact?.emails?.map((email, index) => (
        <View key={index}>
          {renderDetailItem(
            `Email ${index + 1}`,
            email?.address ? email?.address || "N/A" : "N/A"
          )}
        </View>
      ))}

      <View>
        {!personal &&
          renderDetailItem(
            "Tags",
            contact?.tags?.length > 0 ? contact?.tags?.join(" | ") : "N/A"
          )}
      </View>

      {!personal &&
        renderDetailItem(
          "Company",
          contact?.business_details?.companyInformation?.company_name || "N/A"
        )}

      {["Personal Details", "Social Details", "Business Details"].map(
        (section) => (
          <View key={section}>
            <TouchableOpacity
              onPress={() => toggleSection(section)}
              style={styles.sectionHeader}
            >
              <MyText style={styles.sectionTitle}>{section}</MyText>
              <Image
                source={icons.filledArrowDown}
                style={[
                  styles.arrow,
                  {
                    transform: [
                      {
                        rotate: expandedSections.includes(section)
                          ? "180deg"
                          : "0deg",
                      },
                    ],
                  },
                ]}
                resizeMode="contain"
              />
            </TouchableOpacity>

            {expandedSections.includes(section) && (
              <View style={styles.sectionContent}>
                {section === "Personal Details" && (
                  <>
                    <MyText h5 bold style={{ marginBottom: 10 }}>
                      • Basic Details
                    </MyText>
                    {renderDetailItem(
                      "Date of Birth",
                      contact?.dateOfBirth
                        ? formatDate(contact?.dateOfBirth)
                        : ""
                    )}
                    {renderDetailItem("Gender", contact?.gender)}

                    {renderDetailItem(
                      "Official Address",
                      formattedAddress || "Not available"
                    )}

                    {/* Other address array */}
                    {contact?.addresses?.map((address, index) => (
                      <View key={index}>
                        {renderDetailItem(
                          `Other Address ${index + 2}`,
                          `${address?.apartment}, ${address?.street}, ${address?.city}, ${address?.state}, ${address?.country}, ${address?.postalCode}`
                        )}
                      </View>
                    ))}

                    {renderDetailItem("Nationality", contact?.nationality)}
                    <MyText h5 bold style={{ marginBottom: 10 }}>
                      • Other Details
                    </MyText>
                    {/* {renderDetailItem("Nickname", contact?.nickname)} */}
                    {/* nickname is multipe */}
                    {contact?.nickname?.map((nick, index) => (
                      <View key={index}>
                        {renderDetailItem(
                          `Nickname ${index + 1}`,
                          nick || "N/A"
                        )}
                      </View>
                    ))}

                    {/* languages can be multiple */}
                    {contact?.languages?.map((lang, index) => (
                      <View key={index}>
                        {renderDetailItem(
                          `Language ${index + 1}`,
                          lang || "N/A"
                        )}
                      </View>
                    ))}

                    {/* timeZone */}
                    {renderDetailItem(
                      "Time Zone",
                      contact?.timeZone
                        ? contact?.timeZone
                        : "No time zone available"
                    )}

                    {/* Religion */}
                    {contact?.religion?.map((rel, index) => (
                      <View key={index}>
                        {renderDetailItem(
                          `Religion ${index + 1}`,
                          rel || "N/A"
                        )}
                      </View>
                    ))}

                    {renderDetailItem("Marital Status", contact?.maritalStatus)}
                    {/* {renderDetailItem("Hobbies", contact?.hobbies?.join(", "))} */}
                    {contact?.hobbies?.map((hobby, index) => (
                      <View key={index}>
                        {renderDetailItem(`Hobby ${index + 1}`, hobby || "N/A")}
                      </View>
                    ))}

                    {renderDetailItem(
                      "Emergency Contact",
                      contact?.emergency_contact?.contactName &&
                        contact?.emergency_contact?.phoneNumber
                        ? `${contact?.emergency_contact?.contactName} (${contact?.emergency_contact?.phoneNumber_country_code}${contact?.emergency_contact?.phoneNumber}) (${contact?.emergency_contact?.relationship})`
                        : contact?.emergency_contact?.contactName ||
                            contact?.emergency_contact?.phoneNumber
                    )}
                    {renderDetailItem(
                      "Health Insurance",
                      contact?.healthInsurance?.policyNumber &&
                        contact?.healthInsurance?.effectiveDate &&
                        contact?.healthInsurance?.expirationDate
                        ? `${
                            contact?.healthInsurance?.policyNumber
                          } (${formatDate(
                            contact?.healthInsurance?.effectiveDate
                          )} - ${formatDate(
                            contact?.healthInsurance?.expirationDate
                          )})`
                        : contact?.healthInsurance?.policyNumber ||
                            contact?.healthInsurance?.effectiveDate ||
                            contact?.healthInsurance?.expirationDate
                    )}
                    {/* billing_address are multiple in array */}
                    {contact?.billing_address?.map((address, index) => (
                      <View key={index}>
                        {renderDetailItem(
                          `Billing Address ${index + 1}`,
                          [
                            address?.officeBuilding,
                            address?.street,
                            address?.city,
                            address?.state,
                            address?.country,
                            address?.postalCode,
                          ]
                            .filter(Boolean)
                            .join(", ") || "Not available"
                        )}
                      </View>
                    ))}

                    {/* Account Details */}
                    {/* account_details are also multiple */}
                    {contact?.account_details?.map((account, index) => (
                      <View key={index}>
                        {renderDetailItem(
                          `Account Details ${index + 1}`,
                          account?.name &&
                            account?.bank &&
                            account?.accountNumber &&
                            account?.ifscCode
                            ? `${account?.name} (Bank:${account?.bank}, ano.${account?.accountNumber}, IFSC:${account?.ifscCode})`
                            : account?.name ||
                                account?.bank ||
                                account?.accountNumber ||
                                account?.ifscCode ||
                                account?.paypalEmail
                        )}
                      </View>
                    ))}
                    {/* card_details can be multiple */}
                    {contact?.card_details?.map((card, index) => (
                      <View key={index}>
                        {renderDetailItem(
                          `Card Details ${index + 1}`,
                          card?.nameOnCard &&
                            card?.cardNumber &&
                            card?.expiryDate
                            ? `${card?.nameOnCard} (${card?.cardNumber}) (${card?.expiryDate})`
                            : card?.nameOnCard ||
                                card?.cardNumber ||
                                card?.expiryDate,
                          index === contact?.card_details.length - 1 // No line for the last element
                        )}
                      </View>
                    ))}

                    {/* PayPal Details */}
                    {contact?.payPal_account?.map((paypal, index) => (
                      <View key={index}>
                        {renderDetailItem(
                          `PayPal Details ${index + 1}`,
                          paypal?.paypalEmail && paypal?.paypalLink
                            ? `Email:${paypal?.paypalEmail} \n Link:(${paypal?.paypalLink})`
                            : paypal?.paypalEmail || paypal?.paypalLink,
                          index === contact?.payPal_account.length - 1 // No line for the last element
                        )}
                      </View>
                    ))}

                    {/* CryptoWallet */}
                    {contact?.crypto_wallet?.map((wallet, index) => (
                      <View key={index}>
                        {renderDetailItem(
                          `Crypto Wallet ${index + 1}`,
                          wallet?.wallet_address && wallet?.wallet_type
                            ? `${wallet?.wallet_address} (${wallet?.wallet_type})`
                            : wallet?.wallet_address || wallet?.wallet_type,
                          index === contact?.crypto_wallet.length - 1 // No line for the last element
                        )}
                      </View>
                    ))}
                  </>
                )}

                {section === "Business Details" && (
                  <>
                    <MyText h5 bold style={{ marginBottom: 10 }}>
                      • Employee Info
                    </MyText>
                    {renderDetailItem(
                      "Job Title",
                      contact?.business_details?.jobTitle
                    )}

                    {renderDetailItem(
                      "Company Name",
                      contact?.business_details?.companyInformation
                        ?.company_name
                    )}
                    {renderDetailItem(
                      "Department",
                      contact?.business_details?.department
                    )}
                    {renderDetailItem(
                      "Work Phone Number",
                      contact?.business_details?.workContact?.phoneNumber
                    )}
                    {renderDetailItem(
                      "Work Fax Number",
                      contact?.business_details?.workContact?.fax
                    )}
                    {renderDetailItem(
                      "Work Email",
                      contact?.business_details?.workContact?.email
                    )}

                    {/* "resume": "john_doe_resume.pdf", */}

                    {renderDetailItem(
                      "Resume",
                      contact?.business_details?.resume
                        ? contact?.business_details?.resume
                        : "N/A"
                    )}

                    {renderDetailItem(
                      "LinkedIn Profile",
                      contact?.business_details?.linkedinProfile
                        ? contact?.business_details?.linkedinProfile
                        : "N/A"
                    )}
                    {/* twitter */}
                    {renderDetailItem(
                      "Twitter Profile",
                      contact?.business_details?.xProfile
                        ? contact?.business_details?.xProfile
                        : "No Twitter profile available"
                    )}
                    {/* work schedule */}
                    {renderDetailItem(
                      "Work Schedule",
                      contact?.business_details?.workSchedule
                        ? contact?.business_details?.workSchedule
                        : "No work schedule available"
                    )}

                    {contact?.business_details?.certifications?.map(
                      (certification, index) =>
                        renderDetailItem(
                          `Professional Certification ${index + 1}`,
                          certification?.url
                        )
                    )}

                    {renderDetailItem(
                      "Industry",
                      contact?.business_details?.industry
                    )}

                    <MyText h5 bold style={{ marginBottom: 10 }}>
                      • Company Info
                    </MyText>

                    {renderDetailItem(
                      " Name",
                      contact?.business_details?.companyInformation
                        ?.company_name
                    )}
                    {/* Company logo */}
                    {renderDetailItem(
                      "Company Logo",
                      contact?.business_details?.companyInformation
                        ?.company_logo
                        ? contact?.business_details?.companyInformation
                            ?.company_logo
                        : "No company logo available"
                    )}

                    {/* Company phone number */}
                    {/* company phone numberscan be multiple */}
                    {contact?.business_details?.companyInformation?.phone?.map(
                      (phone, index) => (
                        <View key={index}>
                          {renderDetailItem(
                            `Company Phone Number ${index + 1}`,
                            phone?.countryCode
                              ? `${
                                  phone.countryCode.startsWith("+")
                                    ? phone.countryCode
                                    : `+${phone.countryCode}`
                                } ${phone.number}`
                              : phone.number || "N/A"
                          )}
                        </View>
                      )
                    )}

                    {/* company email */}
                    {/* company email can be multiple */}
                    {contact?.business_details?.companyInformation?.email?.map(
                      (email, index) => (
                        <View key={index}>
                          {renderDetailItem(
                            `Company Email ${index + 1}`,
                            email?.address ? email?.address || "N/A" : "N/A"
                          )}
                        </View>
                      )
                    )}

                    {/* Company Fax address */}
                    {/* company fax can be multiple */}
                    {contact?.business_details?.companyInformation?.fax?.map(
                      (fax, index) => (
                        <View key={index}>
                          {renderDetailItem(
                            `Company Fax ${index + 1}`,
                            fax?.number || "N/A"
                          )}
                        </View>
                      )
                    )}

                    {/* Company website */}
                    {renderDetailItem(
                      "Company Website",
                      contact?.business_details?.companyInformation?.website
                        ? contact?.business_details?.companyInformation?.website
                        : "No company website available"
                    )}

                    {/* company address can be multiple */}

                    {contact?.business_details?.business_address?.map(
                      (address, index) => (
                        <View key={index}>
                          {renderDetailItem(
                            `Company Address ${index + 1}`,
                            [
                              address?.officeBuilding,
                              address?.street,
                              address?.city,
                              address?.state,
                              address?.country,
                              address?.postalCode,
                            ]
                              .filter(Boolean)
                              .join(", ") || "N/A"
                          )}
                        </View>
                      )
                    )}

                    {/* Billing address */}
                    {/* billing_address can be multiple */}
                    {contact?.business_details?.billing_address?.map(
                      (address, index) => (
                        <View key={index}>
                          {renderDetailItem(
                            `Billing Address ${index + 1}`,
                            [
                              address?.officeBuilding,
                              address?.street,
                              address?.city,
                              address?.state,
                              address?.country,
                              address?.postalCode,
                            ]
                              .filter(Boolean)
                              .join(", ") || "N/A"
                          )}
                        </View>
                      )
                    )}

                    {/* Company card details */}

                    {/* company card details can be multiple */}
                    {contact?.business_details?.card_details?.map(
                      (card, index) => (
                        <View key={index}>
                          {renderDetailItem(
                            `Company Card Details ${index + 1}`,
                            card?.nameOnCard &&
                              card?.cardNumber &&
                              card?.expiryDate
                              ? `${card?.nameOnCard} (${card?.cardNumber}) (${card?.expiryDate})`
                              : card?.nameOnCard ||
                                  card?.cardNumber ||
                                  card?.expiryDate,
                            index ===
                              contact?.business_details.card_details.length - 1 // No line for the last element
                          )}
                        </View>
                      )
                    )}

                    {/* Company account_details */}
                    {/* company account_details can be multiple */}
                    {contact?.business_details?.account_details?.map(
                      (account, index) => (
                        <View key={index}>
                          {renderDetailItem(
                            `Company Account Details ${index + 1}`,
                            account?.name &&
                              account?.bank &&
                              account?.accountNumber &&
                              account?.ifscCode
                              ? `${account?.name} (Bank:${account?.bank}, ano.${account?.accountNumber}, IFSC:${account?.ifscCode})`
                              : account?.name ||
                                  account?.bank ||
                                  account?.accountNumber ||
                                  account?.ifscCode ||
                                  account?.paypalEmail,
                            index ===
                              contact.business_details.account_details.length -
                                1 // No line for the last element
                          )}
                        </View>
                      )
                    )}

                    {/* Company social media */}
                    {/* company facebook can be multiple */}
                    {contact?.business_details?.companySocialMedia?.facebook?.map(
                      (fb, index) => (
                        <View key={index}>
                          {renderDetailItem(
                            `Company Facebook ${index + 1}`,
                            fb?.url ? fb?.url : "N/A",
                            index ===
                              contact?.business_details?.companySocialMedia
                                ?.facebook.length -
                                1 // No line for the last element
                          )}
                        </View>
                      )
                    )}

                    {/* Instagram */}
                    {Array.isArray(
                      contact?.business_details?.companySocialMedia?.instagram
                    )
                      ? contact.business_details.companySocialMedia.instagram.map(
                          (ig, index) => (
                            <View key={index}>
                              {renderDetailItem(
                                `Company Instagram ${index + 1}`,
                                ig?.url || "N/A",
                                index ===
                                  contact.business_details.companySocialMedia
                                    .instagram.length -
                                    1
                              )}
                            </View>
                          )
                        )
                      : renderDetailItem(
                          "Company Instagram",
                          contact?.business_details?.companySocialMedia
                            ?.instagram?.url || "No company Instagram available"
                        )}

                    {/* Repeat this pattern for other platforms */}

                    {[
                      "twitter",
                      "linkedin",
                      "snapchat",
                      "whatsapp",
                      "telegram",
                      "signal",
                      "skype",
                      "youtube",
                      "twitch",
                      "tiktok",
                    ].map((platform, index, array) =>
                      Array.isArray(
                        contact?.business_details?.companySocialMedia?.[
                          platform
                        ]
                      )
                        ? contact.business_details.companySocialMedia[
                            platform
                          ].map((entry, i) => (
                            <View key={`${platform}-${i}`}>
                              {renderDetailItem(
                                `Company ${capitalize(platform)} ${i + 1}`,
                                entry?.url || "N/A",
                                i ===
                                  contact.business_details.companySocialMedia[
                                    platform
                                  ].length -
                                    1 && index === array.length - 1 // No line after last of last
                              )}
                            </View>
                          ))
                        : renderDetailItem(
                            `Company ${capitalize(platform)}`,
                            contact?.business_details?.companySocialMedia?.[
                              platform
                            ]?.url ||
                              `No company ${capitalize(platform)} available`,
                            index === array.length - 1
                          )
                    )}

                    {/* company messenger IDs */}
                    {[
                      "iMessage",
                      "googleChat",
                      "discord",
                      "wechat",
                      "kik",
                      "line",
                    ].map((platform, index, array) =>
                      Array.isArray(
                        contact?.business_details?.companyMessengerIds?.[
                          platform
                        ]
                      )
                        ? contact.business_details.companyMessengerIds[
                            platform
                          ].map((entry, i) => (
                            <View key={`${platform}-${i}`}>
                              {renderDetailItem(
                                `Company ${platform} ${i + 1}`,
                                entry?.url || "N/A",
                                i ===
                                  contact.business_details.companyMessengerIds[
                                    platform
                                  ].length -
                                    1 && index === array.length - 1
                              )}
                            </View>
                          ))
                        : renderDetailItem(
                            `Company ${platform}`,
                            contact?.business_details?.companyMessengerIds?.[
                              platform
                            ]?.url || `No company ${platform} available`,
                            index === array.length - 1
                          )
                    )}
                  </>
                )}
                {section === "Social Details" && (
                  <>
                                            {/* Facebook */}
{contact?.socialMedia?.facebook?.map((item, index) => (
  <View key={`facebook-${index}`}>
    {renderDetailItem(
      `Facebook ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.facebook.length - 1
    )}
  </View>
))}

{/* Instagram */}
{contact?.socialMedia?.instagram?.map((item, index) => (
  <View key={`instagram-${index}`}>
    {renderDetailItem(
      `Instagram ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.instagram.length - 1
    )}
  </View>
))}

{/* Twitter */}
{contact?.socialMedia?.twitter?.map((item, index) => (
  <View key={`twitter-${index}`}>
    {renderDetailItem(
      `Twitter ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.twitter.length - 1
    )}
  </View>
))}

{/* LinkedIn */}
{contact?.socialMedia?.linkedin?.map((item, index) => (
  <View key={`linkedin-${index}`}>
    {renderDetailItem(
      `LinkedIn ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.linkedin.length - 1
    )}
  </View>
))}

{/* Snapchat */}
{contact?.socialMedia?.snapchat?.map((item, index) => (
  <View key={`snapchat-${index}`}>
    {renderDetailItem(
      `Snapchat ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.snapchat.length - 1
    )}
  </View>
))}

{/* WhatsApp */}
{contact?.socialMedia?.whatsapp?.map((item, index) => (
  <View key={`whatsapp-${index}`}>
    {renderDetailItem(
      `WhatsApp ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.whatsapp.length - 1
    )}
  </View>
))}

{/* Telegram */}
{contact?.socialMedia?.telegram?.map((item, index) => (
  <View key={`telegram-${index}`}>
    {renderDetailItem(
      `Telegram ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.telegram.length - 1
    )}
  </View>
))}

{/* Signal */}
{contact?.socialMedia?.signal?.map((item, index) => (
  <View key={`signal-${index}`}>
    {renderDetailItem(
      `Signal ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.signal.length - 1
    )}
  </View>
))}

{/* Skype */}
{contact?.socialMedia?.skype?.map((item, index) => (
  <View key={`skype-${index}`}>
    {renderDetailItem(
      `Skype ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.skype.length - 1
    )}
  </View>
))}

{/* YouTube */}
{contact?.socialMedia?.youtube?.map((item, index) => (
  <View key={`youtube-${index}`}>
    {renderDetailItem(
      `YouTube ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.youtube.length - 1
    )}
  </View>
))}

{/* Twitch */}
{contact?.socialMedia?.twitch?.map((item, index) => (
  <View key={`twitch-${index}`}>
    {renderDetailItem(
      `Twitch ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.twitch.length - 1
    )}
  </View>
))}

{/* TikTok */}
{contact?.socialMedia?.tiktok?.map((item, index) => (
  <View key={`tiktok-${index}`}>
    {renderDetailItem(
      `TikTok ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.tiktok.length - 1
    )}
  </View>
))}

{/* iMessage */}
{contact?.socialMedia?.iMessage?.map((item, index) => (
  <View key={`iMessage-${index}`}>
    {renderDetailItem(
      `iMessage ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.iMessage.length - 1
    )}
  </View>
))}

{/* Google Chat */}
{contact?.socialMedia?.googleChat?.map((item, index) => (
  <View key={`googleChat-${index}`}>
    {renderDetailItem(
      `Google Chat ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.googleChat.length - 1
    )}
  </View>
))}

{/* Discord */}
{contact?.socialMedia?.discord?.map((item, index) => (
  <View key={`discord-${index}`}>
    {renderDetailItem(
      `Discord ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.discord.length - 1
    )}
  </View>
))}

{/* WeChat */}
{contact?.socialMedia?.wechat?.map((item, index) => (
  <View key={`wechat-${index}`}>
    {renderDetailItem(
      `WeChat ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.wechat.length - 1
    )}
  </View>
))}

{/* Kik */}
{contact?.socialMedia?.kik?.map((item, index) => (
  <View key={`kik-${index}`}>
    {renderDetailItem(
      `Kik ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.kik.length - 1
    )}
  </View>
))}

{/* Line */}
{contact?.socialMedia?.line?.map((item, index) => (
  <View key={`line-${index}`}>
    {renderDetailItem(
      `Line ${index + 1}`,
      item?.url || "N/A",
      index !== contact.socialMedia.line.length - 1
    )}
  </View>
))}
                  </>
                )}
              </View>
            )}
            <View
              style={{
                borderBottomWidth: 1,
                borderBottomColor: "#ccc",
                marginVertical: 10,
              }}
            />
          </View>
        )
      )}
    </View>
  );
};

export default InfoContainerLayout1and2;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  scroll: { paddingBottom: 40 },
  profileContainer: { alignItems: "center" },
  profileImage: {
    width: Dimensions.get("window").width,
    height: Dimensions.get("window").height * 0.35,
  },
  info: {
    alignItems: "center",
    backgroundColor: "#00000080",
    position: "absolute",
    bottom: 0,
    width: "100%",
    paddingVertical: 10,
  },
  name: { color: "#fff" },
  subtitle: { color: "#ccc" },
  infoContainer: { padding: 20 },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 10,
    marginVertical: 5,
  },
  sectionTitle: { fontWeight: "600", fontSize: 16 },
  arrow: { height: 16, width: 16 },
  sectionContent: { marginVertical: 8 },
  detailItem: { marginBottom: 8 },
  label: { fontSize: 14 },
  value: { color: colors.black, textTransform: "capitalize" },
  syncButton: {
    marginHorizontal: 20,
    marginTop: 20,
    backgroundColor: colors.primary,
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: "center",
  },
  syncText: { color: "#fff", fontWeight: "600", fontSize: 16 },
  optionText: {
    alignSelf: "left",
  },
  sortBoxOverlay: {
    position: "absolute",
    top: 80,
    right: 10,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 150,
  },
  sortBoxOption: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
});
