/**
 * Validation utilities for PersonalSection form fields
 */

/**
 * Validate card expiry date (MM/YY format)
 * @param {string} expiry - The expiry date string
 * @returns {string} Error message or empty string if valid
 */
export const validateCardExpiry = (expiry) => {
  if (!expiry || expiry.length < 5) {
    return "Please enter a valid expiry date";
  }

  const parts = expiry.split("/");
  if (parts.length !== 2) {
    return "Invalid format";
  }

  const month = parseInt(parts[0], 10);
  const year = parseInt("20" + parts[1], 10);

  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;

  if (month < 1 || month > 12) {
    return "Invalid month";
  }
  if (year < currentYear) {
    return "Card expired";
  }
  if (year === currentYear && month < currentMonth) {
    return "Card expired";
  }

  return "";
};

/**
 * Validate card number using basic checks
 * @param {string} cardNumber - The card number string
 * @returns {string} Error message or empty string if valid
 */
export const validateCardNumber = (cardNumber) => {
  const cleaned = cardNumber.replace(/\s/g, "");
  if (cleaned.length < 13 || cleaned.length > 19) {
    return "Invalid card number length";
  }

  // Basic format check
  if (!/^\d+$/.test(cleaned)) {
    return "Card number should contain only digits";
  }

  return "";
};

/**
 * Format card number with spaces every 4 digits
 * @param {string} value - The input value
 * @returns {string} Formatted card number
 */
export const formatCardNumber = (value) => {
  const cleanedValue = value.replace(/\D/g, "");
  let formattedValue = "";
  
  for (let i = 0; i < cleanedValue.length; i++) {
    if (i > 0 && i % 4 === 0) {
      formattedValue += " ";
    }
    formattedValue += cleanedValue[i];
  }
  
  return formattedValue;
};

/**
 * Format card expiry date as MM/YY
 * @param {string} value - The input value
 * @returns {string} Formatted expiry date
 */
export const formatCardExpiry = (value) => {
  const cleanedValue = value.replace(/\D/g, "");
  let formattedValue = "";
  
  if (cleanedValue.length > 0) {
    formattedValue = cleanedValue.substring(0, 2);
    
    if (cleanedValue.length > 2) {
      formattedValue += "/" + cleanedValue.substring(2, 4);
    }
  }
  
  return formattedValue;
};

/**
 * Format IFSC code to uppercase and remove invalid characters
 * @param {string} value - The input value
 * @returns {string} Formatted IFSC code
 */
export const formatIFSCCode = (value) => {
  return value.replace(/[^A-Za-z0-9]/g, "").toUpperCase();
};

/**
 * Format numeric input (removes non-numeric characters)
 * @param {string} value - The input value
 * @returns {string} Numeric only string
 */
export const formatNumericInput = (value) => {
  return value.replace(/\D/g, "");
};
