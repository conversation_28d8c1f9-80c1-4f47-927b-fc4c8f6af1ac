import React, { useState } from "react";
import { Image, StyleSheet, TouchableOpacity, View } from "react-native";
import PropTypes from "prop-types";
import MyText from "../../../components/MyText";
import CollapsibleHeader from "./CollapsibleHeader";
import FormField from "./FormField";
import AddressSection from "./AddressSection";
import AdditionalContactField from "./AdditionalContactField";
import CardDetailsSection from "./CardDetailsSection";
import BankAccountSection from "./BankAccountSection";
import { maritalStatusOptions } from "../../../utils/constants";
import { timezones } from "../../../utils/timezones";
import colors from "../../../assets/colors";
import { useFieldVisibility } from "../hooks/useFieldVisibility";
import { useCollapsibleSections } from "../hooks/useCollapsibleSections";
import {
  genderOptions,
  basicDetailsFields,
  otherDetailsFields,
  emergencyContactFields,
  healthInsuranceFields,
} from "../utils/fieldConfigurations";
import icons from "../../../assets/icons";
import CryptoWalletSection from "./CryptoWalletSection";
import AddressSection2 from "./AddressSection2";
import InputField from "../../../components/InputField";

const PersonalSection = React.forwardRef(
  (
    {
      form,
      handleChange,
      errors,
      phoneInputRef,
      onTimezoneDropdownToggle,
      setErrors,
      ownProfileData,
      additionalEmails = [],
      setAdditionalEmails = () => {},
      additionalPhones = [],
      setAdditionalPhones = () => {},
    },
    ref
  ) => {
    // Custom hooks for state management
    const { fieldVisibility, handleVisibilityToggle } = useFieldVisibility();

    const {
      isBasicCollapsed,
      isInsuranceCollapsed,
      isAddressCollapsed,
      isOtherAddressCollapsed,
      isBillingCollapsed,
      isCardCollapsed,
      isAccountCollapsed,
      isEmergencyCollapsed,
      isOtherDetailsCollapsed,
      toggleBasic,
      toggleInsurance,
      toggleAddress,
      toggleOtherAddress,
      toggleBilling,
      toggleCard,
      toggleAccount,
      toggleEmergency,
      toggleOtherDetails,
    } = useCollapsibleSections();

    const [countryPickerVisible, setCountryPickerVisible] = useState(false);

    // Expose methods through the ref
    React.useImperativeHandle(ref, () => ({
      getFieldVisibility: () => fieldVisibility,
    }));

    // Notify parent component when timezone dropdown state changes
    const handleTimezoneDropdownToggle = (isOpen) => {
      if (onTimezoneDropdownToggle) {
        onTimezoneDropdownToggle(isOpen);
      }
    };

    return (
      <View style={{ flex: 1 }}>
        <CollapsibleHeader
          title="Basic Details"
          isCollapsed={isBasicCollapsed}
          onToggle={toggleBasic}
        />

        {!isBasicCollapsed && (
          <View>
            {/* Basic Details Fields */}
            {basicDetailsFields.map((field) => (
              <FormField
                key={field.name}
                type="input"
                name={field.name}
                label={field.label}
                value={form[field.name]}
                onChangeText={(val) => handleChange(field.name, val)}
                error={errors[field.name]}
                isVisible={fieldVisibility[field.name]}
                showVisibilityToggle={!ownProfileData}
                onVisibilityToggle={() => handleVisibilityToggle(field.name)}
                disabled={field.disabled}
                fieldConfig={{
                  keyboardType: field.keyboardType,
                  required: field.required,
                }}
              />
            ))}

            {/* Additional Emails */}
            <AdditionalContactField
              type="email"
              items={additionalEmails}
              setItems={setAdditionalEmails}
              errors={errors}
            />

            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() => setAdditionalEmails([...additionalEmails, ""])}
            >
              + Add more email
            </MyText>

            {/* Primary Phone */}
            <FormField
              type="phone"
              name="phone"
              label="Phone Number*"
              value={form.phone}
              onChangeText={(val) => handleChange("phone", val)}
              error={errors.phone}
              isVisible={true}
              onVisibilityToggle={() => {}}
              disabled={true}
              phoneInputRef={phoneInputRef}
            />

            {/* Additional Phones */}
            <AdditionalContactField
              type="phone"
              items={additionalPhones}
              setItems={setAdditionalPhones}
              errors={errors}
              phoneInputRef={phoneInputRef}
            />

            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() => setAdditionalPhones([...additionalPhones, ""])}
            >
              + Add more Phone Number
            </MyText>

            <MyText bold p style={{ marginVertical: 10 }}>
              Official Address
            </MyText>

            <AddressSection2
              form={form}
              handleChange={handleChange}
              errors={errors}
              fieldVisibility={fieldVisibility}
              handleVisibilityToggle={handleVisibilityToggle}
              showVisibilityToggle={!ownProfileData}
            />

            <MyText bold p style={{ marginVertical: 10 }}>
              Other Addresses
            </MyText>

            {/* <AddressSection
            form={form}
            handleChange={handleChange}
            errors={errors}
            fieldVisibility={fieldVisibility}
            handleVisibilityToggle={handleVisibilityToggle}
            prefix="other"
            disabled={form.isSameAddress}
            sourceForm={form}
          /> */}

            {form.otherAddresses &&
              form.otherAddresses.length > 0 &&
              form.otherAddresses.map((address, index) => (
                <View key={`otherAddress-${index}`}>
                  <AddressSection2
                    form={address}
                    handleChange={(field, value) => {
                      const updatedAddresses = form.otherAddresses.map(
                        (addr, i) =>
                          i === index ? { ...addr, [field]: value } : addr
                      );
                      handleChange("otherAddresses", updatedAddresses);
                    }}
                    noPrefix={true}
                    errors={errors}
                    fieldVisibility={fieldVisibility}
                    handleVisibilityToggle={handleVisibilityToggle}
                    prefix={`Other Address-${index + 1}`}
                    disabled={form.isSameAddress}
                    sourceForm={form}
                    showVisibilityToggle={!ownProfileData}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      const updatedAddresses = form.otherAddresses.filter(
                        (_, i) => i !== index
                      );
                      handleChange("otherAddresses", updatedAddresses);
                    }}
                    style={{
                      position: "absolute",
                      right: 10,
                      top: -10,
                      zIndex: 1,
                      padding: 10,
                    }}
                  >
                    <Image
                      source={icons.minusIcon}
                      style={{ width: 20, height: 20 }}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() =>
                handleChange("otherAddresses", [
                  ...form.otherAddresses,
                  {
                    apartment: "",
                    street: "",
                    city: "",
                    state: "",
                    zipCode: "",
                    country: "",
                  },
                ])
              }
            >
              + Add more address
            </MyText>

            {/* Horizontal Line */}
            {/* <View style={styles.horizontalLine} /> */}
            {/* Horizontal Line Ends */}

            {/* Date of Birth */}
            <FormField
              type="date"
              name="dateOfBirth"
              label="Date of Birth*"
              value={form.dateOfBirth}
              onChangeDate={(date) =>
                handleChange("dateOfBirth", date.toISOString().split("T")[0])
              }
              error={errors.dateOfBirth}
              isVisible={fieldVisibility.dateOfBirth}
              onVisibilityToggle={() => handleVisibilityToggle("dateOfBirth")}
              fieldConfig={{
                placeholder: "Select date of birth",
                maximumDate: new Date(),
              }}
              showVisibilityToggle={!ownProfileData}
            />

            {/* Nationality */}
            <FormField
              type="nationality"
              name="nationality"
              label="Nationality"
              value={form.nationality}
              onSelect={(val) => handleChange("nationality", val)}
              error={errors.nationality}
              isVisible={fieldVisibility.nationality}
              onVisibilityToggle={() => handleVisibilityToggle("nationality")}
              countryPickerVisible={countryPickerVisible}
              setCountryPickerVisible={setCountryPickerVisible}
              showVisibilityToggle={!ownProfileData}
            />

            {/* Gender */}
            <FormField
              type="select"
              name="gender"
              label="Gender"
              value={form.gender}
              onSelect={(item) => handleChange("gender", item.value)}
              error={errors.gender}
              isVisible={fieldVisibility.gender}
              onVisibilityToggle={() => handleVisibilityToggle("gender")}
              data={genderOptions}
              showVisibilityToggle={!ownProfileData}
            />
          </View>
        )}

        {/* <CollapsibleHeader
          title="Home Address"
          isCollapsed={isAddressCollapsed}
          onToggle={toggleAddress}
        />
        {!isAddressCollapsed && (
          <AddressSection
            form={form}
            handleChange={handleChange}
            errors={errors}
            fieldVisibility={fieldVisibility}
            handleVisibilityToggle={handleVisibilityToggle}
          />
        )}

        <CollapsibleHeader
          title="Other Address"
          isCollapsed={isOtherAddressCollapsed}
          onToggle={toggleOtherAddress}
          showCheckbox
          checkboxText="Same as Home"
          isChecked={form.isSameAddress}
          onCheckboxToggle={() => {
            const newValue = !form.isSameAddress;
            handleChange("isSameAddress", newValue);

            if (newValue) {
              handleChange("otherApartment", form.apartment);
              handleChange("otherStreet", form.street);
              handleChange("otherCity", form.city);
              handleChange("otherState", form.state);
              handleChange("otherZipCode", form.zipCode);
              handleChange("otherCountry", form.country);
            }
          }}
        />
        {!isOtherAddressCollapsed && (
          <AddressSection
            form={form}
            handleChange={handleChange}
            errors={errors}
            fieldVisibility={fieldVisibility}
            handleVisibilityToggle={handleVisibilityToggle}
            prefix="other"
            disabled={form.isSameAddress}
            sourceForm={form}
          />
        )} */}
        <CollapsibleHeader
          title="Other Details"
          isCollapsed={isOtherDetailsCollapsed}
          onToggle={toggleOtherDetails}
        />
        {!isOtherDetailsCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            {/* nikename, can be multiple */}

            {form.nickname &&
              form.nickname.length > 0 &&
              form.nickname.map((nickname, index) => (
                <View key={`nickname-${index}`}>
                  <FormField
                    type="input"
                    name={`nickname-${index}`}
                    label={
                      form.nickname.length > 0
                        ? `Nickname ${index + 1}`
                        : "Nickname"
                    }
                    value={nickname}
                    onChangeText={(val) => {
                      const updatedNicknames = [...form.nickname];
                      updatedNicknames[index] = val;
                      handleChange("nickname", updatedNicknames);
                    }}
                    error={errors[`nickname-${index}`]}
                    isVisible={fieldVisibility[`nickname-${index}`]}
                    onVisibilityToggle={() =>
                      handleVisibilityToggle(`nickname-${index}`)
                    }
                    fieldConfig={{
                      placeholder: `Enter nickname ${index + 1}`,
                    }}
                    containerStyle={{ flex: 1 }}
                    showVisibilityToggle={!ownProfileData}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      const updatedNicknames = form.nickname.filter(
                        (_, i) => i !== index
                      );
                      handleChange("nickname", updatedNicknames);
                    }}
                    style={{
                      position: "absolute",
                      right: 10,
                      top: -10,
                      zIndex: 1,
                      padding: 10,
                    }}
                  >
                    <Image
                      source={icons.minusIcon}
                      style={{ width: 20, height: 20 }}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() => handleChange("nickname", [...form.nickname, ""])}
            >
              + Add more nickname
            </MyText>

            {/* Marital Status */}
            <FormField
              type="select"
              name="maritalStatus"
              label="Marital Status"
              value={form.maritalStatus}
              onSelect={(item) => handleChange("maritalStatus", item.value)}
              error={errors.maritalStatus}
              isVisible={fieldVisibility.maritalStatus}
              onVisibilityToggle={() => handleVisibilityToggle("maritalStatus")}
              data={maritalStatusOptions}
              showVisibilityToggle={!ownProfileData}
            />

            {/* Spouse Name - conditional */}
            {form.maritalStatus === "Married" && (
              <FormField
                type="input"
                name="spouseName"
                label="Spouse Name"
                value={form.spouseName}
                onChangeText={(val) => handleChange("spouseName", val)}
                error={errors.spouseName}
                isVisible={fieldVisibility.spouseName}
                onVisibilityToggle={() => handleVisibilityToggle("spouseName")}
                showVisibilityToggle={!ownProfileData}
              />
            )}

            {/* Children's Names - can be multiple */}
            {form?.childrenNames &&
              form?.childrenNames.length > 0 &&
              form?.childrenNames?.map((name, index) => (
                <View key={`childrenName-${index}`}>
                  <FormField
                    type="input"
                    name={`childrenName-${index}`}
                    label={`Child's Name ${index + 1}`}
                    value={name}
                    onChangeText={(val) => {
                      const updatedChildrenNames = [...form.childrenNames];
                      updatedChildrenNames[index] = val;
                      handleChange("childrenNames", updatedChildrenNames);
                    }}
                    error={errors[`childrenName-${index}`]}
                    isVisible={fieldVisibility[`childrenName-${index}`]}
                    onVisibilityToggle={() =>
                      handleVisibilityToggle(`childrenName-${index}`)
                    }
                    fieldConfig={{
                      placeholder: `Enter child's name ${index + 1}`,
                    }}
                    containerStyle={{ flex: 1 }}
                    showVisibilityToggle={!ownProfileData}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      const updatedChildrenNames = form.childrenNames.filter(
                        (_, i) => i !== index
                      );
                      handleChange("childrenNames", updatedChildrenNames);
                    }}
                    style={{
                      position: "absolute",
                      right: 10,
                      top: -10,
                      zIndex: 1,
                      padding: 10,
                    }}
                  >
                    <Image
                      source={icons.minusIcon}
                      style={{ width: 20, height: 20 }}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() =>
                handleChange("childrenNames", [...form.childrenNames, ""])
              }
            >
              + Add more child
            </MyText>

            {/* Hobbies/Interest - can be multiple */}
            {form.hobbies &&
              form.hobbies.length > 0 &&
              form.hobbies.map((hobby, index) => (
                <View key={`hobby-${index}`}>
                  <FormField
                    type="input"
                    name={`hobby-${index}`}
                    label={`Hobby/Interest ${index + 1}`}
                    value={hobby}
                    onChangeText={(val) => {
                      const updatedHobbies = [...form.hobbies];
                      updatedHobbies[index] = val;
                      handleChange("hobbies", updatedHobbies);
                    }}
                    error={errors[`hobby-${index}`]}
                    isVisible={fieldVisibility[`hobby-${index}`]}
                    onVisibilityToggle={() =>
                      handleVisibilityToggle(`hobby-${index}`)
                    }
                    fieldConfig={{
                      placeholder: `Enter hobby or interest ${index + 1}`,
                    }}
                    containerStyle={{ flex: 1 }}
                    showVisibilityToggle={!ownProfileData}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      const updatedHobbies = form.hobbies.filter(
                        (_, i) => i !== index
                      );
                      handleChange("hobbies", updatedHobbies);
                    }}
                    style={{
                      position: "absolute",
                      right: 10,
                      top: -10,
                      zIndex: 1,
                      padding: 10,
                    }}
                  >
                    <Image
                      source={icons.minusIcon}
                      style={{ width: 20, height: 20 }}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() => handleChange("hobbies", [...form.hobbies, ""])}
            >
              + Add more hobby
            </MyText>

            {/* native language */}
            <FormField
              type="input"
              name="nativeLanguage"
              label="Native Language"
              value={form.nativeLanguage}
              onChangeText={(val) => handleChange("nativeLanguage", val)}
              error={errors.nativeLanguage}
              isVisible={fieldVisibility.nativeLanguage}
              onVisibilityToggle={() =>
                handleVisibilityToggle("nativeLanguage")
              }
              fieldConfig={{
                placeholder: "Enter your native language",
              }}
              showVisibilityToggle={!ownProfileData}
            />

            {form.otherLanguages.map((lang, index) => (
              <View key={`otherLanguage-${index}`}>
                <FormField
                  type="input"
                  name={`otherLanguage-${index}`}
                  label={`Other Language ${index + 1}`}
                  value={lang}
                  onChangeText={(val) => {
                    const updatedLanguages = [...form.otherLanguages];
                    updatedLanguages[index] = val;
                    handleChange("otherLanguages", updatedLanguages);
                  }}
                  error={errors[`otherLanguage-${index}`]}
                  isVisible={fieldVisibility[`otherLanguage-${index}`]}
                  onVisibilityToggle={() =>
                    handleVisibilityToggle(`otherLanguage-${index}`)
                  }
                  fieldConfig={{
                    placeholder: `Enter other language ${index + 1}`,
                  }}
                  containerStyle={{ flex: 1 }}
                  showVisibilityToggle={!ownProfileData}
                />
                <TouchableOpacity
                  onPress={() => {
                    const updatedLanguages = form.otherLanguages.filter(
                      (_, i) => i !== index
                    );
                    handleChange("otherLanguages", updatedLanguages);
                  }}
                  style={{
                    position: "absolute",
                    right: 10,
                    top: -10,
                    zIndex: 1,
                    padding: 10,
                  }}
                >
                  <Image
                    source={icons.minusIcon}
                    style={{ width: 20, height: 20 }}
                  />
                </TouchableOpacity>
              </View>
            ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() =>
                handleChange("otherLanguages", [...form.otherLanguages, ""])
              }
            >
              + Add more language
            </MyText>

            {form.religion.map((religion, index) => (
              <View key={`religion-${index}`}>
                <FormField
                  type="select"
                  name={`religion-${index}`}
                  label={`Religion ${index + 1}`}
                  value={religion}
                  onSelect={(item) => {
                    const updatedReligions = [...form.religion];
                    updatedReligions[index] = item.value;
                    handleChange("religion", updatedReligions);
                  }}
                  data={[
                    { value: "Christianity", label: "Christianity" },
                    { value: "Islam", label: "Islam" },
                    { value: "Hinduism", label: "Hinduism" },
                    { value: "Buddhism", label: "Buddhism" },
                    { value: "Sikhism", label: "Sikhism" },
                    { value: "Judaism", label: "Judaism" },
                    { value: "Atheism", label: "Atheism" },
                    { value: "Agnosticism", label: "Agnosticism" },
                    { value: "Other", label: "Other" },
                  ]}
                  error={errors[`religion-${index}`]}
                  isVisible={fieldVisibility[`religion-${index}`]}
                  onVisibilityToggle={() =>
                    handleVisibilityToggle(`religion-${index}`)
                  }
                  fieldConfig={{
                    placeholder: `Enter religion ${index + 1}`,
                  }}
                  containerStyle={{ flex: 1 }}
                  showVisibilityToggle={!ownProfileData}
                />
                <TouchableOpacity
                  onPress={() => {
                    const updatedReligions = form.religion.filter(
                      (_, i) => i !== index
                    );
                    handleChange("religion", updatedReligions);
                  }}
                  style={{
                    position: "absolute",
                    right: 10,
                    top: -10,
                    zIndex: 1,
                    padding: 10,
                  }}
                >
                  <Image
                    source={icons.minusIcon}
                    style={{ width: 20, height: 20 }}
                  />
                </TouchableOpacity>
              </View>
            ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() => handleChange("religion", [...form.religion, ""])}
            >
              + Add more religion
            </MyText>

            {/* Preferred Contact method */}
            <FormField
              type="select"
              name="preferredContactMethod"
              label="Preferred Contact Method"
              value={form.preferredContactMethod}
              onSelect={(item) =>
                handleChange("preferredContactMethod", item.value)
              }
              error={errors.preferredContactMethod}
              isVisible={fieldVisibility.preferredContactMethod}
              onVisibilityToggle={() =>
                handleVisibilityToggle("preferredContactMethod")
              }
              data={[
                { value: "Email", label: "Email" },
                { value: "Phone", label: "Phone" },
                { value: "Facebook", label: "Facebook" },
                { value: "Instagram", label: "Instagram" },
                { value: "Twitter", label: "Twitter" },
                { value: "Snapchat", label: "Snapchat" },
                { value: "Skype", label: "Skype" },
                { value: "iMessage", label: "iMessage" },
                { value: "Google Chat", label: "Google Chat" },
                { value: "Discord", label: "Discord" },
                { value: "WeChat", label: "WeChat" },
                { value: "Kik", label: "Kik" },
                { value: "Line", label: "Line" },
                { value: "WhatsApp", label: "WhatsApp" },
                { value: "SMS", label: "SMS" },
                { value: "Telegram", label: "Telegram" },
                { value: "Signal", label: "Signal" },
              ]}
              showVisibilityToggle={!ownProfileData}
            />

            {/* <FormField
              type="phone"
              name="secondaryPhone"
              label="Secondary Phone No."
              value={form.secondaryPhone}
              onChangeText={(val) => handleChange("secondaryPhone", val)}
              error={errors.secondaryPhone}
              isVisible={fieldVisibility.secondaryPhone}
              onVisibilityToggle={() =>
                handleVisibilityToggle("secondaryPhone")
              }
              phoneInputRef={phoneInputRef}
            /> */}

            {/* Timezone */}
            <FormField
              type="select"
              name="timezone"
              label="Timezone"
              value={form.timezone}
              onSelect={(item) => handleChange("timezone", item.value)}
              error={errors.timezone}
              isVisible={fieldVisibility.timezone}
              onVisibilityToggle={() => handleVisibilityToggle("timezone")}
              data={timezones}
              onDropdownToggle={handleTimezoneDropdownToggle}
              showVisibilityToggle={!ownProfileData}
            />

            {/* Notes, can be multiple */}
            {form.notes &&
              form.notes.length > 0 &&
              form.notes.map((note, index) => (
                <View key={`note-${index}`}>
                  <FormField
                    type="input"
                    name={`note-${index}`}
                    label={`Note ${index + 1}`}
                    value={note}
                    onChangeText={(val) => {
                      const updatedNotes = [...form.notes];
                      updatedNotes[index] = val;
                      handleChange("notes", updatedNotes);
                    }}
                    error={errors[`note-${index}`]}
                    isVisible={fieldVisibility[`note-${index}`]}
                    onVisibilityToggle={() =>
                      handleVisibilityToggle(`note-${index}`)
                    }
                    fieldConfig={{
                      placeholder: `Enter note ${index + 1}`,
                    }}
                    containerStyle={{ flex: 1 }}
                    showVisibilityToggle={!ownProfileData}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      const updatedNotes = form.notes.filter(
                        (_, i) => i !== index
                      );
                      handleChange("notes", updatedNotes);
                    }}
                    style={{
                      position: "absolute",
                      right: 10,
                      top: -10,
                      zIndex: 1,
                      padding: 10,
                    }}
                  >
                    <Image
                      source={icons.minusIcon}
                      style={{ width: 20, height: 20 }}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() => handleChange("notes", [...form.notes, ""])}
            >
              + Add more note
            </MyText>

            <MyText bold p style={{ marginVertical: 10 }}>
              Emergency Information
            </MyText>

            <View style={{ zIndex: 1, position: "relative" }}>
              {/* Emergency Contact Fields */}
              {emergencyContactFields.map((field) => (
                <FormField
                  key={field.name}
                  type="input"
                  name={field.name}
                  label={field.label}
                  value={form[field.name]}
                  onChangeText={(val) => handleChange(field.name, val)}
                  error={errors[field.name]}
                  isVisible={fieldVisibility[field.name]}
                  onVisibilityToggle={() => handleVisibilityToggle(field.name)}
                  fieldConfig={{
                    keyboardType: field.keyboardType,
                  }}
                  showVisibilityToggle={!ownProfileData}
                />
              ))}

              {/* Emergency Phone */}
              <FormField
                type="phone"
                name="emergencyPhone"
                label="Emergency Phone No."
                value={form.emergencyPhone}
                onChangeText={(val) => handleChange("emergencyPhone", val)}
                error={errors.emergencyPhone}
                isVisible={fieldVisibility.emergencyPhone}
                onVisibilityToggle={() =>
                  handleVisibilityToggle("emergencyPhone")
                }
                phoneInputRef={phoneInputRef}
              />
            </View>

            <MyText bold p style={{ marginVertical: 10 }}>
              Health Insurance Information
            </MyText>

            <View style={{ zIndex: 1, position: "relative" }}>
              {/* Health Insurance Fields */}
              {healthInsuranceFields.map((field) => (
                <FormField
                  key={field.name}
                  type="input"
                  name={field.name}
                  label={field.label}
                  value={form[field.name]}
                  onChangeText={(val) => handleChange(field.name, val)}
                  error={errors[field.name]}
                  isVisible={fieldVisibility[field.name]}
                  onVisibilityToggle={() => handleVisibilityToggle(field.name)}
                  fieldConfig={{
                    keyboardType: field.keyboardType,
                  }}
                  showVisibilityToggle={!ownProfileData}
                />
              ))}

              {/* Date Fields */}
              <FormField
                type="date"
                name="effectiveDate"
                label="Effective Date"
                value={form.effectiveDate}
                onChangeDate={(date) =>
                  handleChange(
                    "effectiveDate",
                    date.toISOString().split("T")[0]
                  )
                }
                error={errors.effectiveDate}
                isVisible={fieldVisibility.effectiveDate}
                onVisibilityToggle={() =>
                  handleVisibilityToggle("effectiveDate")
                }
                fieldConfig={{
                  placeholder: "Select effective date",
                }}
                showVisibilityToggle={!ownProfileData}
              />

              <FormField
                type="date"
                name="expirationDate"
                label="Expiration Date"
                value={form.expirationDate}
                onChangeDate={(date) =>
                  handleChange(
                    "expirationDate",
                    date.toISOString().split("T")[0]
                  )
                }
                error={errors.expirationDate}
                isVisible={fieldVisibility.expirationDate}
                onVisibilityToggle={() =>
                  handleVisibilityToggle("expirationDate")
                }
                fieldConfig={{
                  placeholder: "Select expiration date",
                }}
                showVisibilityToggle={!ownProfileData}
              />
            </View>

            <MyText bold p style={{ marginVertical: 10 }}>
              Financial Information
            </MyText>

            <MyText bold p style={{ marginVertical: 10 }}>
              Billing Addresses
            </MyText>

            {form.billingAddresses &&
              form.billingAddresses.length > 0 &&
              form.billingAddresses.map((address, index) => (
                <View key={`billingAddress-${index}`}>
                  <AddressSection2
                    form={address}
                    handleChange={(field, value) => {
                      const updatedBillingAddresses = [
                        ...form.billingAddresses,
                      ];
                      updatedBillingAddresses[index][field] = value;
                      handleChange("billingAddresses", updatedBillingAddresses);
                    }}
                    noPrefix={true}
                    errors={errors}
                    fieldVisibility={fieldVisibility}
                    handleVisibilityToggle={handleVisibilityToggle}
                    prefix={`Billing Address-${index + 1}`}
                    disabled={form.isSameBillingAddress}
                    sourceForm={form}
                    showVisibilityToggle={!ownProfileData}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      const updatedBillingAddresses =
                        form.billingAddresses.filter((_, i) => i !== index);
                      handleChange("billingAddresses", updatedBillingAddresses);
                    }}
                    style={{
                      position: "absolute",
                      right: 10,
                      top: -10,
                      zIndex: 1,
                      padding: 10,
                    }}
                  >
                    <Image
                      source={icons.minusIcon}
                      style={{ width: 20, height: 20 }}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() =>
                handleChange("billingAddresses", [
                  ...form.billingAddresses,
                  {
                    apartment: "",
                    street: "",
                    city: "",
                    state: "",
                    zipCode: "",
                    country: "",
                  },
                ])
              }
            >
              + Add more billing address
            </MyText>

            <MyText bold p style={{ marginVertical: 10 }}>
              Card Information
            </MyText>

            {/* card details, can be multiple */}

            {form.cardDetails &&
              form.cardDetails.length > 0 &&
              form.cardDetails.map((card, index) => (
                <View key={`cardDetails-${index}`}>
                  <CardDetailsSection
                    form={card}
                    handleChange={(field, value) => {
                      const updatedCardDetails = [...form.cardDetails];
                      updatedCardDetails[index][field] = value;
                      handleChange("cardDetails", updatedCardDetails);
                    }}
                    errors={errors}
                    setErrors={setErrors}
                    fieldVisibility={fieldVisibility}
                    handleVisibilityToggle={handleVisibilityToggle}
                    prefix={`Card Details-${index + 1}`}
                    disabled={form.isSameBillingAddress}
                    sourceForm={form}
                    showVisibilityToggle={!ownProfileData}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      const updatedCardDetails = form.cardDetails.filter(
                        (_, i) => i !== index
                      );
                      handleChange("cardDetails", updatedCardDetails);
                    }}
                    style={{
                      position: "absolute",
                      right: 10,
                      top: -10,
                      zIndex: 1,
                      padding: 10,
                    }}
                  >
                    <Image
                      source={icons.minusIcon}
                      style={{ width: 20, height: 20 }}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() =>
                handleChange("cardDetails", [
                  ...form.cardDetails,
                  {
                    cardName: "",
                    cardNumber: "",
                    cardExpiry: "",
                    cardCvv: "",
                  },
                ])
              }
            >
              + Add more card details
            </MyText>

            {/* bank account details, can be multiple */}

            <MyText bold p style={{ marginVertical: 10 }}>
              Bank Account Information
            </MyText>

            {form.bankAccountDetails &&
              form.bankAccountDetails.length > 0 &&
              form.bankAccountDetails.map((account, index) => (
                <View key={`bankAccountDetails-${index}`}>
                  <BankAccountSection
                    form={account}
                    handleChange={(field, value) => {
                      const updatedBankAccounts = [...form.bankAccountDetails];
                      updatedBankAccounts[index][field] = value;
                      handleChange("bankAccountDetails", updatedBankAccounts);
                    }}
                    errors={errors}
                    fieldVisibility={fieldVisibility}
                    handleVisibilityToggle={handleVisibilityToggle}
                    prefix={`Bank Account-${index + 1}`}
                    disabled={form.isSameBillingAddress}
                    sourceForm={form}
                    showVisibilityToggle={!ownProfileData}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      const updatedBankAccounts =
                        form.bankAccountDetails.filter((_, i) => i !== index);
                      handleChange("bankAccountDetails", updatedBankAccounts);
                    }}
                    style={{
                      position: "absolute",
                      right: 10,
                      top: -10,
                      zIndex: 1,
                      padding: 10,
                    }}
                  >
                    <Image
                      source={icons.minusIcon}
                      style={{ width: 20, height: 20 }}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() =>
                handleChange("bankAccountDetails", [
                  ...form.bankAccountDetails,
                  {
                    accountName: "",
                    bankName: "",
                    accountNumber: "",
                    ifscCode: "",
                  },
                ])
              }
            >
              + Add more bank account details
            </MyText>

            <MyText bold p style={{ marginVertical: 10 }}>
              PayPal Account
            </MyText>

            {form.paypalDetails &&
              form.paypalDetails.length > 0 &&
              form.paypalDetails.map((paypal, index) => (
                <View key={`paypalDetails-${index}`}>
                  <FormField
                    type="input"
                    name={`paypalEmail-${index}`}
                    label={`PayPal Email ${index + 1}`}
                    value={paypal.paypalEmail}
                    onChangeText={(val) => {
                      const updatedPaypalDetails = [...form.paypalDetails];
                      updatedPaypalDetails[index].paypalEmail = val;
                      handleChange("paypalDetails", updatedPaypalDetails);
                    }}
                    error={errors[`paypalEmail-${index}`]}
                    isVisible={fieldVisibility[`paypalEmail-${index}`]}
                    onVisibilityToggle={() =>
                      handleVisibilityToggle(`paypalEmail-${index}`)
                    }
                    fieldConfig={{
                      placeholder: `Enter PayPal email ${index + 1}`,
                    }}
                    containerStyle={{ flex: 1 }}
                    showVisibilityToggle={!ownProfileData}
                  />

                  {/* paypal link */}
                  <FormField
                    type="input"
                    name={`paypalLink-${index}`}
                    label={`PayPal Link ${index + 1}`}
                    value={paypal.paypalLink}
                    onChangeText={(val) => {
                      const updatedPaypalDetails = [...form.paypalDetails];
                      updatedPaypalDetails[index].paypalLink = val;
                      handleChange("paypalDetails", updatedPaypalDetails);
                    }}
                    error={errors[`paypalLink-${index}`]}
                    isVisible={fieldVisibility[`paypalLink-${index}`]}
                    onVisibilityToggle={() =>
                      handleVisibilityToggle(`paypalLink-${index}`)
                    }
                    fieldConfig={{
                      placeholder: `Enter PayPal link ${index + 1}`,
                    }}
                    containerStyle={{ flex: 1 }}
                    showVisibilityToggle={!ownProfileData}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      const updatedPaypalDetails = form.paypalDetails.filter(
                        (_, i) => i !== index
                      );
                      handleChange("paypalDetails", updatedPaypalDetails);
                    }}
                    style={{
                      position: "absolute",
                      right: 10,
                      top: -10,
                      zIndex: 1,
                      padding: 10,
                    }}
                  >
                    <Image
                      source={icons.minusIcon}
                      style={{ width: 20, height: 20 }}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() =>
                handleChange("paypalDetails", [
                  ...form.paypalDetails,
                  {
                    paypalEmail: "",
                    paypalLink: "",
                  },
                ])
              }
            >
              + Add more PayPal details
            </MyText>

            <MyText bold p style={{ marginVertical: 10 }}>
              Cryptocurrency Wallet
            </MyText>

            {/* cryptoWallet */}
            {form.cryptoWalletDetails &&
              form.cryptoWalletDetails.length > 0 &&
              form.cryptoWalletDetails.map((wallet, index) => (
                <View key={`cryptoWallet-${index}`}>
                  <CryptoWalletSection
                    form={wallet}
                    handleChange={(field, value) => {
                      const updatedCryptoWallets = [
                        ...form.cryptoWalletDetails,
                      ];
                      updatedCryptoWallets[index][field] = value;
                      handleChange("cryptoWalletDetails", updatedCryptoWallets);
                    }}
                    errors={errors}
                    fieldVisibility={fieldVisibility}
                    handleVisibilityToggle={handleVisibilityToggle}
                    prefix={`Crypto Wallet-${index + 1}`}
                    disabled={form.isSameBillingAddress}
                    sourceForm={form}
                    showVisibilityToggle={!ownProfileData}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      // return;
                      const updatedCryptoWallets =
                        form.cryptoWalletDetails.filter((_, i) => i !== index);
                      handleChange("cryptoWalletDetails", updatedCryptoWallets);
                    }}
                    style={{
                      position: "absolute",
                      right: 10,
                      top: -10,
                      zIndex: 1,
                      padding: 10,
                    }}
                  >
                    <Image
                      source={icons.minusIcon}
                      style={{ width: 20, height: 20 }}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() =>
                handleChange("cryptoWalletDetails", [
                  ...form.cryptoWalletDetails,
                  {
                    walletAddress: "",
                    walletType: "",
                  },
                ])
              }
            >
              + Add more crypto wallet
            </MyText>
          </View>
        )}
      </View>
    );
  }
);

PersonalSection.propTypes = {
  form: PropTypes.shape({
    firstName: PropTypes.string,
    middleName: PropTypes.string,
    lastName: PropTypes.string,
    nickname: PropTypes.string,
    email: PropTypes.string,
    phone: PropTypes.string,
    dateOfBirth: PropTypes.string,
    gender: PropTypes.string,
    nationality: PropTypes.string,
    maritalStatus: PropTypes.string,
    spouseName: PropTypes.string,
    // Health Insurance Information
    policyNumber: PropTypes.string,
    insuranceProvider: PropTypes.string,
    policyPeriod: PropTypes.string,
    effectiveDate: PropTypes.string,
    expirationDate: PropTypes.string,
    sumInsured: PropTypes.string,
    // Address fields
    apartment: PropTypes.string,
    street: PropTypes.string,
    city: PropTypes.string,
    state: PropTypes.string,
    zipCode: PropTypes.string,
    country: PropTypes.string,
    // Billing Address fields
    isSameBillingAddress: PropTypes.bool,
    billingApartment: PropTypes.string,
    billingStreet: PropTypes.string,
    billingCity: PropTypes.string,
    billingState: PropTypes.string,
    billingZipCode: PropTypes.string,
    billingCountry: PropTypes.string,
    // Card Details fields
    cardName: PropTypes.string,
    cardNumber: PropTypes.string,
    cardExpiry: PropTypes.string,
    cardCvv: PropTypes.string,
    // Bank Account Details fields
    accountName: PropTypes.string,
    bankName: PropTypes.string,
    accountNumber: PropTypes.string,
    ifscCode: PropTypes.string,
    // Other Address fields
    isSameAddress: PropTypes.bool,
    otherApartment: PropTypes.string,
    otherStreet: PropTypes.string,
    otherCity: PropTypes.string,
    otherState: PropTypes.string,
    otherZipCode: PropTypes.string,
    otherCountry: PropTypes.string,
    // Other Details fields
    secondaryEmail: PropTypes.string,
    secondaryPhone: PropTypes.string,
    personalWebsite: PropTypes.string,
    hobbies: PropTypes.string,
    religion: PropTypes.string,
    preferredContactMethod: PropTypes.string,
    timezone: PropTypes.string,
    // Emergency Contact fields
    emergencyContactName: PropTypes.string,
    emergencyContactRelationship: PropTypes.string,
    emergencyEmail: PropTypes.string,
    emergencyPhone: PropTypes.string,
    emergencyAddress: PropTypes.string,
  }).isRequired,
  handleChange: PropTypes.func.isRequired,
  errors: PropTypes.object,
  setErrors: PropTypes.func,
  phoneInputRef: PropTypes.object,
  onTimezoneDropdownToggle: PropTypes.func,
};

const styles = StyleSheet.create({
  horizontalLine: {
    borderBottomWidth: 1,
    borderBottomColor: "#ccc",
    marginVertical: 16,
  },
});

export default PersonalSection;
