import React from "react";
import { View } from "react-native";
import InputField from "../../../components/InputField";
import SelectField from "../../../components/SelectField";
import DatePicker from "../../../components/DatePicker";
import PhoneNumberField from "../../../components/PhoneNumberField";
import NationalitySelector from "../../../components/NationalitySelector";

/**
 * Reusable form field component that renders different field types
 * @param {Object} props - Component props
 * @param {string} props.type - Field type ('input', 'select', 'date', 'phone', 'nationality')
 * @param {string} props.name - Field name
 * @param {string} props.label - Field label
 * @param {*} props.value - Field value
 * @param {Function} props.onChangeText - Change handler for input fields
 * @param {Function} props.onSelect - Select handler for select fields
 * @param {Function} props.onChangeDate - Date change handler
 * @param {string} props.error - Error message
 * @param {boolean} props.isVisible - Visibility state
 * @param {Function} props.onVisibilityToggle - Visibility toggle handler
 * @param {boolean} props.disabled - Disabled state
 * @param {Array} props.data - Data for select fields
 * @param {Object} props.fieldConfig - Additional field configuration
 * @returns {JSX.Element} Form field component
 */
const FormField = ({
  type = "input",
  name,
  label,
  value,
  onChangeText,
  onSelect,
  onChangeDate,
  error,
  isVisible = true,
  onVisibilityToggle,
  disabled = false,
  data = [],
  fieldConfig = {},
  phoneInputRef,
  countryPickerVisible,
  setCountryPickerVisible,
  onDropdownToggle,
  showVisibilityToggle = true,
  ...otherProps
}) => {
  const commonProps = {
    label,
    error,
    showVisibilityToggle: showVisibilityToggle,
    isVisible,
    onVisibilityToggle,
    disabled,
    ...fieldConfig,
    ...otherProps,
  };

  switch (type) {
    case "input":
      return (
        <InputField
          {...commonProps}
          value={value}
          onChangeText={onChangeText}
        />
      );

    case "select":
      return (
        <SelectField
          {...commonProps}
          data={data}
          defaultValue={value}
          onSelect={onSelect}
          backgroundColor="#f2f2f2"
          height={48}
          width="100%"
          showIcon={true}
          onDropdownToggle={onDropdownToggle}
        />
      );

    case "date":
      return (
        <DatePicker
          {...commonProps}
          value={value}
          onChangeDate={onChangeDate}
          placeholder={fieldConfig.placeholder || "Select date"}
          maximumDate={fieldConfig.maximumDate}
        />
      );

    case "phone":
      return (
        <PhoneNumberField
          {...commonProps}
          labelP={true}
          labelMedium={true}
          value={value}
          phoneInputRef={phoneInputRef}
          onChangeRaw={onChangeText}
          setCountryCode={() => {}}
          setError={() => {}}
        />
      );

    case "nationality":
      return (
        <NationalitySelector
          {...commonProps}
          value={value}
          onSelect={onSelect}
          isPickerVisible={countryPickerVisible}
          setPickerVisible={setCountryPickerVisible}
        />
      );

    default:
      return (
        <InputField
          {...commonProps}
          value={value}
          onChangeText={onChangeText}
        />
      );
  }
};

export default FormField;
