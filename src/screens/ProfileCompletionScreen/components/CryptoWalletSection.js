import React from "react";
import { View } from "react-native";
import <PERSON><PERSON><PERSON> from "./FormField";

/**
 * Specialized component for crypto wallet details
 * @param {Object} props - Component props
 * @param {Object} props.form - Form data
 * @param {Function} props.handleChange - Change handler
 * @param {Object} props.errors - Error messages
 * @param {Object} props.fieldVisibility - Field visibility state
 * @param {Function} props.handleVisibilityToggle - Visibility toggle handler
 * @returns {JSX.Element} Crypto wallet section component
 */
const CryptoWalletSection = ({
  form,
  handleChange,
  errors,
  fieldVisibility,
  handleVisibilityToggle,
  showVisibilityToggle = true,
}) => {
  const cryptoWalletFields = [
    {
      name: "walletAddress",
      label: "Wallet Address",
      placeholder: "Enter wallet address",
      keyboardType: "default",
      autoCapitalize: "none",
      maxLength: 100,
      type: "input",
    },
    {
      name: "walletType",
      label: "Wallet Type",
      placeholder: "e.g. Bitcoin, Ethereum",
      keyboardType: "default",
      autoCapitalize: "words",
      maxLength: 30,
      type: "select",
      options: [
        { label: "Bitcoin (BTC)", value: "bitcoin" },
        { label: "Ethereum (ETH)", value: "ethereum" },
        { label: "Binance Smart Chain (BSC)", value: "bsc" },
        { label: "Solana (SOL)", value: "solana" },
        { label: "Polygon (MATIC)", value: "polygon" },
        { label: "Tron (TRX)", value: "tron" },
        { label: "Ripple (XRP)", value: "ripple" },
        { label: "Cardano (ADA)", value: "cardano" },
        { label: "Litecoin (LTC)", value: "litecoin" },
        { label: "Dogecoin (DOGE)", value: "dogecoin" },
        { label: "Other", value: "other" },
      ],
    },
  ];

  const renderField = (field) => (
    <FormField
      key={field.name}
      type={field.type}
      name={field.name}
      label={field.label}
      value={form[field.name]}
      {...(field.type === "input"
        ? { onChangeText: (val) => handleChange(field.name, val) }
        : { onSelect: (value) => handleChange(field.name, value.label) })}
      data={field.options}
      error={errors[field.name]}
      isVisible={fieldVisibility[field.name]}
      onVisibilityToggle={() => handleVisibilityToggle(field.name)}
      fieldConfig={{
        placeholder: field.placeholder,
        keyboardType: field.keyboardType,
        autoCapitalize: field.autoCapitalize,
        maxLength: field.maxLength,
      }}
      showVisibilityToggle={showVisibilityToggle}
    />
  );

  return (
    <View style={{ zIndex: 1, position: "relative" }}>
      {cryptoWalletFields.map(renderField)}
    </View>
  );
};

export default CryptoWalletSection;
