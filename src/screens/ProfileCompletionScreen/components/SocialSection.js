import React, { useState } from "react";
import { Image, TouchableOpacity, View } from "react-native";
import PropTypes from "prop-types";
import InputField from "../../../components/InputField";
import CollapsibleHeader from "./CollapsibleHeader";
import icons from "../../../assets/icons";
import MyText from "../../../components/MyText";
import colors from "../../../assets/colors";

const SocialSection = React.forwardRef(
  ({ form, handleChange, errors, ownProfileData }, ref) => {
    const [isSocialsCollapsed, setIsSocialsCollapsed] = useState(false);
    const [isMessengerCollapsed, setIsMessengerCollapsed] = useState(true);

    // Visibility states for social media fields
    const [fieldVisibility, setFieldVisibility] = useState({
      personalWebsite: true,
      facebook: true,
      instagram: true,
      twitter: true,
      linkedIn: true,
      snapchat: true,
      whatsapp: true,
      telegram: true,
      signal: true,
      youtube: true,
      twitch: true,
      tiktok: true,
      discord: true,
      googleChat: true,
      iMessage: true,
      wechat: true,
      kik: true,
      slack: true,
      line: true,
      skype: true,
    });

    // Handle visibility toggle for a field
    const handleVisibilityToggle = (fieldName) => {
      setFieldVisibility((prev) => ({
        ...prev,
        [fieldName]: !prev[fieldName],
      }));
    };

    // Expose methods through the ref
    React.useImperativeHandle(ref, () => ({
      getFieldVisibility: () => fieldVisibility,
    }));

    const renderMultipleFields = ({
      key,
      label,
      placeholder,
      keyboardType = "url",
      icon,
    }) => (
      <>
        {(form[key] || []).map((value, index) => (
          <View key={`${key}-${index}`}>
            <InputField
              label={
                (form[key] || []).length > 1 ? `${label} ${index + 1}` : label
              }
              value={value}
              onChangeText={(val) => {
                const updatedArr = [...(form[key] || [])];
                updatedArr[index] = val;
                handleChange(key, updatedArr);
              }}
              error={errors[key]?.[index]}
              placeholder={placeholder}
              keyboardType={keyboardType}
              showVisibilityToggle={!ownProfileData}
              isVisible={fieldVisibility[key]}
              onVisibilityToggle={() => handleVisibilityToggle(key)}
            />
            {(form[key] || []).length > 1 && (
              <TouchableOpacity
                onPress={() => {
                  const updatedArr = [...(form[key] || [])];
                  updatedArr.splice(index, 1);
                  handleChange(key, updatedArr);
                }}
                style={{
                  position: "absolute",
                  right: 10,
                  top: -10,
                  zIndex: 1,
                  padding: 10,
                }}
              >
                <Image
                  source={icons.minusIcon}
                  resizeMode="contain"
                  style={{ width: 20, height: 20 }}
                />
              </TouchableOpacity>
            )}
          </View>
        ))}
        <MyText
          underline
          p
          medium
          style={{ color: colors.primary, marginBottom: 10 }}
          onPress={() => {
            handleChange(key, [...form[key], ""]);
          }}
        >
          + Add another {label} link
        </MyText>
      </>
    );

    return (
      <View>
        <CollapsibleHeader
          title="Socials"
          isCollapsed={isSocialsCollapsed}
          onToggle={() => setIsSocialsCollapsed(!isSocialsCollapsed)}
        />
        {!isSocialsCollapsed && (
          <View style={{ zIndex: 2, position: "relative" }}>
            {/* personal website */}
            {renderMultipleFields({
              key: "personalWebsite",
              label: "Personal Website",
              placeholder: "https://example.com",
              keyboardType: "url",
            })}

            {/* social media links */}
            {renderMultipleFields({
              key: "facebook",
              label: "Facebook",
              placeholder: "https://facebook.com/username",
            })}
            {renderMultipleFields({
              key: "instagram",
              label: "Instagram",
              placeholder: "https://instagram.com/username",
            })}
            {renderMultipleFields({
              key: "twitter",
              label: "Twitter",
              placeholder: "https://twitter.com/username",
            })}
            {renderMultipleFields({
              key: "snapchat",
              label: "Snapchat",
              placeholder: "https://snapchat.com/add/username",
            })}
            {renderMultipleFields({
              key: "whatsapp",
              label: "WhatsApp",
              placeholder: "https://wa.me/phonenumber",
            })}
            {renderMultipleFields({
              key: "telegram",
              label: "Telegram",
              placeholder: "https://t.me/username",
            })}
            {renderMultipleFields({
              key: "signal",
              label: "Signal",
              placeholder: "signal://username",
            })}
            {renderMultipleFields({
              key: "skype",
              label: "Skype",
              placeholder: "https://skype.com/username",
            })}
            {renderMultipleFields({
              key: "youtube",
              label: "YouTube",
              placeholder: "https://youtube.com/c/channelname",
            })}
            {renderMultipleFields({
              key: "twitch",
              label: "Twitch",
              placeholder: "https://twitch.tv/username",
            })}
            {renderMultipleFields({
              key: "tiktok",
              label: "TikTok",
              placeholder: "https://tiktok.com/@username",
            })}
            {/* {renderMultipleFields({
          key: "linkedIn",
          label: "LinkedIn",
          placeholder: "https://linkedin.com/in/username",
        })} */}
          </View>
        )}

        <CollapsibleHeader
          title="Personal Messenger IDs"
          isCollapsed={isMessengerCollapsed}
          onToggle={() => setIsMessengerCollapsed(!isMessengerCollapsed)}
        />
        {!isMessengerCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            {renderMultipleFields({
              key: "iMessage",
              label: "iMessage",
              placeholder: "https://imessage.com/username",
            })}
            {renderMultipleFields({
              key: "discord",
              label: "Discord",
              placeholder: "https://discord.com/invite/username",
            })}
            {renderMultipleFields({
              key: "googleChat",
              label: "Google Chat",
              placeholder: "https://chat.google.com/username",
            })}
            {renderMultipleFields({
              key: "wechat",
              label: "WeChat",
              placeholder: "https://wechat.com/username",
            })}
            {renderMultipleFields({
              key: "kik",
              label: "Kik",
              placeholder: "https://kik.com/username",
            })}
            {/* {renderMultipleFields({
          key: "slack",
          label: "Slack",
          placeholder: "https://slack.com/username",
        })} */}
            {renderMultipleFields({
              key: "line",
              label: "Line",
              placeholder: "https://line.me/username",
            })}
          </View>
        )}
      </View>
    );
  }
);

SocialSection.propTypes = {
  form: PropTypes.shape({
    facebook: PropTypes.string,
    instagram: PropTypes.string,
    twitter: PropTypes.string,
    linkedIn: PropTypes.string,
    snapchat: PropTypes.string,
    whatsapp: PropTypes.string,
    telegram: PropTypes.string,
    signal: PropTypes.string,
    youtube: PropTypes.string,
    twitch: PropTypes.string,
    tiktok: PropTypes.string,
  }).isRequired,
  handleChange: PropTypes.func.isRequired,
  errors: PropTypes.object,
};

export default SocialSection;
