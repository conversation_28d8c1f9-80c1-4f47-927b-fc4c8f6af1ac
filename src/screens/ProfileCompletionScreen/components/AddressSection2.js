import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import { Country, State, City } from 'country-state-city';
import FormField from './FormField';
import { addressFields } from '../utils/fieldConfigurations';
import CustomDropDown from '../../../components/SelectField';

const AddressSection2 = ({
  form,
  handleChange,
  errors,
  fieldVisibility,
  handleVisibilityToggle,
  prefix = '',
  disabled = false,
  sourceForm = null,
  noPrefix = false,
  showVisibilityToggle = true,
}) => {
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);

  // console.log(`AddressSection2 rendered with form:`, form);
  

  const getFieldName = (baseName) => baseName;

  const getFieldValue = (baseName) => {
    const fieldName = getFieldName(baseName);
    return disabled && sourceForm ? sourceForm[baseName] : form[fieldName];
  };

  // const countryCode = getFieldValue('country');
  // const stateCode = getFieldValue('state');
  const [countryCode, setCountryCode] = useState("");
  const [stateCode, setStateCode] = useState("");

  useEffect(() => {
    if (countryCode) {
      const fetchedStates = State.getStatesOfCountry(countryCode);
      console.log(`Fetched states for country ${countryCode}:`, fetchedStates);
      
      setStates(fetchedStates || []);
    } else {
      setStates([]);
    }
    setCities([]); // Clear cities when country changes
  }, [countryCode]);

  useEffect(() => {
    if (countryCode && stateCode) {
      console.log(`Fetching cities for country ${countryCode} and state ${stateCode}`);
      
      const fetchedCities = City.getCitiesOfState(countryCode, stateCode);
      console.log(`Fetched cities for state ${stateCode}:`, fetchedCities);
      
      setCities(fetchedCities || []);
    } else {
      setCities([]);
    }
  }, [stateCode]);

  return (
    <View style={{ zIndex: 1, position: 'relative' }}>
      {addressFields.map((field) => {
        const baseName = field.name;
        const fieldName = noPrefix ? baseName : getFieldName(baseName);
        const label = prefix
          ? `${prefix.charAt(0).toUpperCase()}${prefix.slice(1)} ${field.label}`
          : field.label;

        // Render CustomDropDown for country
        if (baseName === 'country') {
          return (
            <CustomDropDown
              key={fieldName}
              label={label}
              labelStyle={{ textTransform: 'capitalize' }}
              data={Country.getAllCountries().map(c => ({ label: c.name, value: c.isoCode }))}
              defaultValue={getFieldValue(baseName)}
              search={true}
              onSelect={(item) => {
                console.log(`Selected country`, item);
                handleChange(fieldName, item.label);
                setCountryCode(item.value);
                setStateCode(''); // Reset state and city when country changes
                setStates([]); // Clear states when country changes
                setCities([]); // Clear cities when country changes
              }}
              disabled={disabled}
            />
          );
        }

        // Render CustomDropDown for state
        if (baseName === 'state') {
          return (
            <CustomDropDown
              key={fieldName}
              label={label}
              data={states.map(s => ({ label: s.name, value: s.isoCode }))}
              defaultValue={getFieldValue(baseName)}
              search={true}
              onSelect={(item) => {
                handleChange(fieldName, item.label);
                setStateCode(item.value);
                setCities([]); // Clear cities when state changes
              }}
              disabled={disabled || states.length === 0}
            />
          );
        }

        // Render CustomDropDown for city
        if (baseName === 'city') {
          return (
            <CustomDropDown
              key={fieldName}
              label={label}
              data={cities.map(c => ({ label: c.name, value: c.name }))}
              defaultValue={getFieldValue(baseName)}
              search={true}
              onSelect={(item) => {
                handleChange(fieldName, item.value);

              }}
              disabled={disabled || cities.length === 0}
            />
          );
        }

        // Default to FormField for other fields
        return (
          <FormField
            key={fieldName}
            type="input"
            name={fieldName}
            label={label}
            value={getFieldValue(baseName)}
            onChangeText={(val) => handleChange(fieldName, val)}
            error={errors[fieldName]}
            isVisible={fieldVisibility[fieldName]}
            onVisibilityToggle={() => handleVisibilityToggle(fieldName)}
            disabled={disabled}
            showVisibilityToggle={showVisibilityToggle}
          />
        );
      })}
    </View>
  );
};

export default AddressSection2;
