import { useState } from 'react';
import { initialFieldVisibility } from '../utils/fieldConfigurations';

/**
 * Custom hook to manage field visibility state
 * @returns {Object} Field visibility state and toggle function
 */
export const useFieldVisibility = () => {
  const [fieldVisibility, setFieldVisibility] = useState(initialFieldVisibility);

  /**
   * Handle visibility toggle for a field
   * @param {string} fieldName - The name of the field to toggle
   */
  const handleVisibilityToggle = (fieldName) => {
    setFieldVisibility((prev) => ({
      ...prev,
      [fieldName]: !prev[fieldName],
    }));
  };

  /**
   * Get visibility state for a specific field
   * @param {string} fieldName - The name of the field
   * @returns {boolean} Visibility state
   */
  const getFieldVisibility = (fieldName) => {
    return fieldVisibility[fieldName] ?? true;
  };

  /**
   * Set visibility for multiple fields at once
   * @param {Object} visibilityUpdates - Object with field names as keys and boolean values
   */
  const setMultipleFieldVisibility = (visibilityUpdates) => {
    setFieldVisibility((prev) => ({
      ...prev,
      ...visibilityUpdates,
    }));
  };

  return {
    fieldVisibility,
    handleVisibilityToggle,
    getFieldVisibility,
    setMultipleFieldVisibility,
  };
};
