import { useState } from "react";

/**
 * Custom hook to manage collapsible section states
 * @returns {Object} Section states and toggle functions
 */
export const useCollapsibleSections = () => {
  const [isBasicCollapsed, setIsBasicCollapsed] = useState(false);
  const [isInsuranceCollapsed, setIsInsuranceCollapsed] = useState(true);
  const [isAddressCollapsed, setIsAddressCollapsed] = useState(true);
  const [isOtherAddressCollapsed, setIsOtherAddressCollapsed] = useState(false);
  const [isBillingCollapsed, setIsBillingCollapsed] = useState(true);
  const [isCardCollapsed, setIsCardCollapsed] = useState(true);
  const [isAccountCollapsed, setIsAccountCollapsed] = useState(true);
  const [isEmergencyCollapsed, setIsEmergencyCollapsed] = useState(true);
  const [isOtherDetailsCollapsed, setIsOtherDetailsCollapsed] = useState(true);

  const toggleBasic = () => setIsBasicCollapsed(!isBasicCollapsed);
  const toggleInsurance = () => setIsInsuranceCollapsed(!isInsuranceCollapsed);
  const toggleAddress = () => setIsAddressCollapsed(!isAddressCollapsed);
  const toggleOtherAddress = () =>
    setIsOtherAddressCollapsed(!isOtherAddressCollapsed);
  const toggleBilling = () => setIsBillingCollapsed(!isBillingCollapsed);
  const toggleCard = () => setIsCardCollapsed(!isCardCollapsed);
  const toggleAccount = () => setIsAccountCollapsed(!isAccountCollapsed);
  const toggleEmergency = () => setIsEmergencyCollapsed(!isEmergencyCollapsed);
  const toggleOtherDetails = () =>
    setIsOtherDetailsCollapsed(!isOtherDetailsCollapsed);

  return {
    // States
    isBasicCollapsed,
    isInsuranceCollapsed,
    isAddressCollapsed,
    isOtherAddressCollapsed,
    isBillingCollapsed,
    isCardCollapsed,
    isAccountCollapsed,
    isEmergencyCollapsed,
    isOtherDetailsCollapsed,

    // Toggle functions
    toggleBasic,
    toggleInsurance,
    toggleAddress,
    toggleOtherAddress,
    toggleBilling,
    toggleCard,
    toggleAccount,
    toggleEmergency,
    toggleOtherDetails,
  };
};
