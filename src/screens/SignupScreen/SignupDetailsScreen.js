import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
} from "react-native";
import BackButton from "../../components/BackButton";
import CustomCheckbox from "../../components/CustomCheckbox"; // your custom checkbox
import PasswordInput from "../../components/PasswordInput";
import { useDispatch } from "react-redux";
import { signup, socialSignup } from "../../redux/features/authSlice";
import PhoneNumberField from "../../components/PhoneNumberField";
import AppModal from "../../components/AppModal";
import InputField from "../../components/InputField";
import { showToast } from "../../utils/toastConfig";
import { GoogleSignin } from "@react-native-google-signin/google-signin";

const SignupDetailsScreen = ({ navigation, route }) => {
  const phoneInput = useRef(null);

  const prefillData = route?.params?.prefillData || {};

  const [form, setForm] = useState({
    firstName: prefillData.firstName || "",
    middleName: prefillData.middleName || "",
    lastName: prefillData.lastName || "",
    email: prefillData.email || "",
    phone: "",
    password: "",
    confirmPassword: "",
    agreed: false,
    callingCode: "974", // Default from PhoneNumberField
    countryCode: "QA", // Default from PhoneNumberField
  });
  // Add useEffect to log form changes
  useEffect(() => {
    console.log("Form updated:", form);
  }, [form]);

  const [modalVisible, setModalVisible] = useState({
    visible: false,
    title: "",
    description: "",
    singleButton: false,
    secondButtonText: "",
    firstButtonText: "",
    onClose: () => setModalVisible((prev) => ({ ...prev, visible: false })),
    onSubmit: null,
  });

  const [errors, setErrors] = useState({});

  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();

  const handleChange = (field, value) => {
    console.log(`Changing ${field} to:`, value);

    // Special handling for callingCode and countryCode to prevent auto-reset to default values
    if (field === "callingCode" || field === "countryCode") {
      console.log(`Setting ${field} to ${value} (preventing auto-reset)`);
    }

    setForm((prevForm) => {
      const updatedForm = { ...prevForm, [field]: value };
      return updatedForm;
    });

    setErrors((prevErrors) => ({ ...prevErrors, [field]: "" })); // Clear error on input
  };

  const validate = () => {
    let newErrors = {};
const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%?&])[A-Za-z\d@$!%?&]{8,}$/;
    // --- FIRST NAME ---
    const first = form.firstName || "";
    if (!first.trim()) {
      newErrors.firstName = "Required";
    } else if (/\d/.test(first)) {
      newErrors.firstName = "First name cannot contain numbers";
    }

    // --- MIDDLE NAME (optional) ---
    if (form.middleName !== undefined && form.middleName !== null) {
      const middle = form.middleName;
      // if user typed only spaces
      if (middle && !middle.trim()) {
        newErrors.middleName = "Middle name cannot be blank";
      } else if (/\d/.test(middle)) {
        newErrors.middleName = "Middle name cannot contain numbers";
      }
    }

    // --- LAST NAME ---
    const last = form.lastName || "";
    if (!last.trim()) {
      newErrors.lastName = "Required";
    } else if (/\d/.test(last)) {
      newErrors.lastName = "Last name cannot contain numbers";
    }

    // --- EMAIL ---
    // RFC-5322 simpleish regex: one @, no spaces, at least one dot-part after @
    const emailRe = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!form.email || !emailRe.test(form.email)) {
      newErrors.email = "Valid email required";
    }

    // --- PHONE ---
    if (!form.phone) {
      newErrors.phone = "Required";
    }

    // --- PASSWORD ---
    if (!form.password) {
      newErrors.password = "Required";
    } else if (form.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
    } else if (!passwordRegex.test(form.password)) {
    newErrors.password = "Password must contain at least one uppercase, one lowercase, one number and one special character";
  }

    // --- CONFIRM PASSWORD ---
    if (!form.confirmPassword) {
      newErrors.confirmPassword = "Required";
    } else if (form.password !== form.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    // --- AGREE TO TERMS ---
    if (!form.agreed) {
      newErrors.agreed = "You must agree to continue";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (validate()) {
      console.log("Form Data:", form);
    } else {
      console.log("Validation Failed");
      return;
    }

    const payload = {
      firstName: form.firstName,
      ...(form.middleName?.trim() && { middleName: form.middleName.trim() }),
      lastName: form.lastName,
      email: form.email,
      phone_number: form.phone,
      country_code: `+${form.callingCode}`, // Add + prefix to calling code
      calling_code: form.countryCode,
      password: form.password,
      governmentId: "ABC123456", // Replace with actual government ID if needed
    };

    console.log("Payload:", payload);

    // return;
    try {
      setLoading(true);
      // Properly await the dispatch result
      await GoogleSignin.signOut();
      const action = await dispatch(signup(payload));
      console.log("Signup Action:", JSON.stringify(action));

      // Extract the actual response data
      const response = action.payload;
      console.log("Signup Response:", JSON.stringify(response));

      if (response?.success) {
        console.log("Signup successful");
        navigation.navigate("TwoFactorScreen", {
          showEmailVerification: true,
          showPhoneVerification: true,
          showProgressLine: true,
          type: "signup",
          userID: response?.data?.userId,
        });
      } else {
        console.error("Signup failed:", response);
        // Check different possible error message locations
        const errorMessage =
          response?.message ||
          response?.data?.message ||
          "Signup failed. Please try again.";
        showToast("error", errorMessage);

        return;
      }
    } catch (error) {
      console.error("Signup Error:", error);
      setErrors({ ...errors, server: "Signup failed. Please try again." });
    } finally {
      setLoading(false);
    }
  };
  const handleSubmitForSocial = async () => {
    if (validate()) {
      console.log("Form Data:", form);
    } else {
      console.log("Validation Failed");
      return;
    }

    const payload = {
      email: form.email,
      email_verified: true,
      firstName: form.firstName,
      ...(form.middleName?.trim() && { middleName: form.middleName.trim() }),
      lastName: form.lastName,
      social_login_id: prefillData.social_login_id,
      phone_number: form.phone,
      country_code: `+${form.callingCode}`, // Add + prefix to calling code
      calling_code: form.countryCode,
      governmentId: "ABC123456",
      password: form.password,
    };
    console.log("Payload for Social Login:", payload);

    // return;

    try {
      setLoading(true);
      // Properly await the dispatch result
      const action = await dispatch(socialSignup(payload));
      console.log("Social Signup Action:", JSON.stringify(action));

      // Extract the actual response data
      const response = action.payload;
      console.log("Social Signup Response:", JSON.stringify(response));

      if (response?.success) {
        console.log("Social signup successful");
        navigation.navigate("TwoFactorScreen", {
          showEmailVerification: false,
          showPhoneVerification: true,
          showProgressLine: true,
          type: "socialSignup",
          userID: response?.data?.userId,
        });
      } else {
        console.error("Social signup failed:", response);
        // Check different possible error message locations
        const errorMessage =
          response?.message ||
          response?.data?.message ||
          "Social signup failed. Please try again.";
        showToast("error", errorMessage);
        return;
      }
    } catch (error) {
      console.error("Signup Error:", error);
      setErrors({ ...errors, server: "Signup failed. Please try again." });
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView behavior={"padding"} style={{ flex: 1 }}>
        <ScrollView contentContainerStyle={styles.scroll}>
          <BackButton />
          <Text style={styles.title}>Let’s get you started</Text>
          <Text style={styles.subtitle}>
            Enter the following details to sign up
          </Text>
          {/* Progress Line */}
          <View style={styles.progressContainer}>
            <View style={styles.progressLine} />
          </View>

          <InputField
            label="First Name*"
            disabled={prefillData.firstName ? true : false}
            value={form.firstName}
            onChangeText={(val) => handleChange("firstName", val)}
            error={errors.firstName}
            maxLength={35}
          />

          {/* middle name */}
          <InputField
            label="Middle Name"
            disabled={prefillData.middleName ? true : false}
            value={form.middleName}
            onChangeText={(val) => handleChange("middleName", val)}
            error={errors.middleName}
            maxLength={35}
          />

          <InputField
            label="Last Name*"
            disabled={prefillData.lastName ? true : false}
            value={form.lastName}
            onChangeText={(val) => handleChange("lastName", val)}
            error={errors.lastName}
            maxLength={35}
          />
          <InputField
            label="Email*"
            disabled={prefillData.email ? true : false}
            value={form.email}
            onChangeText={(val) => handleChange("email", val)}
            error={errors.email}
          />
          <Text style={styles.label}>Phone Number*</Text>
          <PhoneNumberField
            value={form.phone}
            phoneInputRef={phoneInput}
            onChangeRaw={(val) => handleChange("phone", val)}
            onCodeChange={(val) => {
              console.log("PhoneNumberField onCodeChange:", val);
              handleChange("callingCode", val);
            }}
            onCountryCodeChange={(val) => {
              console.log("PhoneNumberField onCountryCodeChange:", val);
              handleChange("countryCode", val);
            }}
            error={errors.phone}
            setError={(msg) => setErrors((prev) => ({ ...prev, phone: msg }))}
            countryCodeProp={form.countryCode}
            callingCodeProp={form.callingCode}
          />

          <PasswordInput
            label="Password*"
            placeholder="Enter password"
            value={form.password}
            onChangeText={(val) => handleChange("password", val)}
            error={errors.password}
          />

          <PasswordInput
            label="Re-enter Password*"
            placeholder="Enter confirm password"
            value={form.confirmPassword}
            onChangeText={(val) => handleChange("confirmPassword", val)}
            error={errors.confirmPassword}
          />

          <View style={styles.agreeRow}>
            <CustomCheckbox
              checked={form.agreed}
              onChange={() => handleChange("agreed", !form.agreed)}
            />
            <Text style={styles.agreeText}>
              {"  "}I have read and agree to all the{" "}
              <Text
                style={[styles.linkText]}
                onPress={() => navigation.navigate("TermsConditionsScreen")}
              >
                Terms
              </Text>{" "}
              &{" "}
              <Text
                style={[styles.linkText]}
                onPress={() => navigation.navigate("PrivacyPolicyScreen")}
              >
                Policies
              </Text>
            </Text>
          </View>
          {errors.agreed && (
            <Text style={styles.errorText}>{errors.agreed}</Text>
          )}

          <TouchableOpacity
            disabled={loading}
            activeOpacity={0.8}
            style={[styles.nextButton, loading && { backgroundColor: "#ccc" }]}
            onPress={prefillData.email ? handleSubmitForSocial : handleSubmit}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.nextText}>Next</Text>
            )}
          </TouchableOpacity>

          <Text style={styles.footer}>
            Already have an account?{" "}
            <Text
              style={styles.loginLink}
              onPress={() => navigation.navigate("Login")}
            >
              Log In
            </Text>
          </Text>
        </ScrollView>
        <AppModal {...modalVisible} />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  scroll: { padding: 20, paddingBottom: 40 },
  title: { fontSize: 22, fontWeight: "700", marginTop: 10 },
  subtitle: { fontSize: 14, color: "gray", marginBottom: 10 },
  progressContainer: {
    height: 3,
    backgroundColor: "#eee",
    marginBottom: 30,
  },
  progressLine: {
    height: 3,
    width: "50%",
    backgroundColor: "#2E64FE",
    borderRadius: 50,
  },
  linkText: {
    color: "blue",
    textDecorationLine: "underline",
  },
  label: {  marginBottom: 6 },
  inputContainer: { marginBottom: 15 },
  input: {
    backgroundColor: "#f2f2f2",
    borderRadius: 10,
    paddingHorizontal: 12,
    height: 48,
  },
  phoneContainer: {
    backgroundColor: "#f2f2f2",
    borderRadius: 10,
    height: 48,
    marginBottom: 5,
    borderWidth: 1,
    borderColor: "#f2f2f2",
  },
  phoneTextContainer: {
    backgroundColor: "#f2f2f2",
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
  },
  errorBorder: {
    borderColor: "red",
    borderWidth: 1,
  },
  errorText: {
    color: "red",
    fontSize: 12,
    marginTop: 2,
  },
  agreeRow: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 12,
  },
  agreeText: {
    flex: 1,
    fontSize: 13,
  },
  nextButton: {
    backgroundColor: "#2E64FE",
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: "center",
    marginTop: 10,
  },
  nextText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  footer: {
    marginTop: 20,
    textAlign: "center",
  },
  loginLink: {
    color: "red",
    fontWeight: "500",
  },
});

export default SignupDetailsScreen;
