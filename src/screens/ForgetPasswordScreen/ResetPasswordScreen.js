import React, { useState } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  ScrollView,
} from 'react-native';
import BackButton from '../../components/BackButton';
import PasswordInput from '../../components/PasswordInput';
import { useDispatch } from 'react-redux';
import { resetPassword } from '../../redux/features/authSlice';
import BottomModal from '../../components/BottomModal';
import icons from '../../assets/icons';
import MyText from '../../components/MyText';
import { PrimaryButton } from '../../components/Button';
import colors from '../../assets/colors';

const ResetPasswordScreen = ({ navigation, route }) => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [errors, setErrors] = useState({});
   const [modalVisible, setModalVisible] = useState(false); 

  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();

  const validate = () => {
    const newErrors = {};

    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%?&])[A-Za-z\d@$!%?&]{8,}$/;
  
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    }  else if (!passwordRegex.test(password)) {
    newErrors.password = "Password must contain at least one uppercase, one lowercase, one number and one special character";
  }
  
    if (!confirmPassword) {
      newErrors.confirmPassword = 'Confirmation is required';
    } else if (password && password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
  
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

   const handleContinue = () => {
    setModalVisible(false);
    // Navigate somewhere or trigger next action
    navigation.replace("Login"); // or any relevant screen
  };

  const handleSubmit = async () => {
    if (validate()) {
      const payload = {
        userId: route.params.userId,
        newPassword: password,
      }

      try {
        setLoading(true);
        const response = await dispatch(resetPassword(payload));
        console.log("Response:", JSON.stringify(response));

        if (response.payload.success) {
          setModalVisible(true); // Show success modal
          // Optionally, you can navigate to another screen or reset the form
          // navigation.navigate("Login");
          // Reset form fields
          setPassword('');
          setConfirmPassword('');
          setErrors({});
        }
        else {
          setErrors(prev => ({ ...prev, password: response?.payload?.data?.message }));
        }

      } catch (error) {
        console.error("Error:", error);
        setErrors(prev => ({ ...prev, password: 'An error occurred. Please try again.' }));
      } finally {
        setLoading(false);
      }

    
      
    }
  };

  return (
    <SafeAreaView style={styles.container}>
    <ScrollView>
      <View style={styles.inner}>
        <BackButton />

        <Text style={styles.title}>Change Password</Text>
        <Text style={styles.subtitle}>You can now change the password</Text>

        <PasswordInput
          label="New Password*"
          placeholder="Enter new password"
          value={password}
          onChangeText={text => {
            setPassword(text);
            if (errors.password) setErrors(prev => ({ ...prev, password: '' }));
          }}
          error={errors.password}
        />

        <PasswordInput
          label="Re-enter New Password*"
          placeholder="Re-enter new password"
          value={confirmPassword}
          onChangeText={text => {
            setConfirmPassword(text);
            if (errors.confirmPassword) setErrors(prev => ({ ...prev, confirmPassword: '' }));
          }}
          error={errors.confirmPassword}
        />

        <TouchableOpacity disabled={loading}  
        style={[styles.submitButton, loading && { backgroundColor: 'gray' }]} 
        activeOpacity={0.8}
        onPress={handleSubmit}>
          {loading ? <ActivityIndicator size="small" color="#fff" /> :
            <Text style={styles.submitText}>Submit</Text>}
        </TouchableOpacity>
      </View>
      </ScrollView>
         <BottomModal
        isVisible={modalVisible}
        onClose={() => setModalVisible(false)}
        title={""} 
      >
        <View style={styles.centered}>
          <Image
            source={icons.successIcon} 
            style={styles.checkIcon}
            resizeMode="contain"
          />
          <MyText h5 semibold style={styles.successText}>
            Password Changed
          </MyText>
          <MyText p style={styles.subText}>
            Your password has been changed successfully!
          </MyText>
          <PrimaryButton
            title="Continue"
            onPress={handleContinue}
            style={styles.continueButton}
          />
        </View>
      </BottomModal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  inner: {
    padding: 20,
    flex: 1,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    marginTop: 10,
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 14,
    color: 'gray',
    marginBottom: 30,
  },
  submitButton: {
    marginTop: 'auto',
    backgroundColor: '#2E64FE',
    paddingVertical: 16,
    borderRadius: 10,
    alignItems: 'center',
  },
  submitText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
   centered: {
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
  },
  checkIcon: {
    width: 80,
    height: 80,
    marginBottom: 20,
  },
  successText: {
    marginBottom: 8,
    textAlign: "center",
  },
  subText: {
    marginBottom: 20,
    textAlign: "center",
    color: colors.gray1,
  },
  button: {
    backgroundColor: "#2F52FF",
    paddingVertical: 14,
    paddingHorizontal: 40,
    borderRadius: 12,
  },
});

export default ResetPasswordScreen;
