import React, { useState } from "react";
import {
  View,
  Text,
  SafeAreaView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import BackButton from "../../components/BackButton";
import InputField from "../../components/InputField";
import { useDispatch } from "react-redux";
import { forgetPasswrord } from "../../redux/features/authSlice";
import AppLoader from "../../components/AppLoader";

const ForgotPasswordScreen = ({ navigation }) => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");

  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();

  const validate = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!email.trim()) {
      setError("Email is required");
      return false;
    }

    if (!emailRegex.test(email)) {
      setError("Enter a valid email address");
      return false;
    }

    setError("");
    return true;
  };

  const handleNext = async () => {
    if (validate()) {
      const payload = {
        email: email,
      }

      try {
        setLoading(true);
        const response = await dispatch(forgetPasswrord(payload));
        console.log("Response:", JSON.stringify(response));

        if(response.payload.success) {
          navigation.navigate("TwoFactorScreen", {
            showEmailVerification: true,
            showPhoneVerification: false,
            subheading: "An OTP has been sent on your mail ID",
            type: "forgotPassword",
            email: email,
          });
        } else {
          setError(response?.payload?.data?.message);
        }

        
      } catch (error) {
        console.log("Error:", error);
        setLoading(false);
      } finally {
        setLoading(false);
      }
      
      



      // navigation.navigate("TwoFactorScreen", {
      //   showEmailVerification: false,
      //   showPhoneVerification: true,
      // });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.inner}>
        <BackButton />

        <Text style={styles.title}>Forgot Password?</Text>
        <Text style={styles.subtitle}>Don't worry! We are here for you</Text>

        <InputField
          label="Email*"
          placeholder="Enter Email"
          value={email}
          onChangeText={(val) => {
            setEmail(val);
            if (error) setError("");
          }}
          error={error}
        />

        <TouchableOpacity onPress={handleNext} style={styles.button}>
          <Text style={styles.buttonText}>Next</Text>
        </TouchableOpacity>
      </View>
      <AppLoader isLoading={loading} />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  inner: {
    padding: 20,
    flex: 1,
    justifyContent: "flex-start",
  },
  title: {
    fontSize: 22,
    fontWeight: "700",
    marginTop: 10,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: "gray",
    marginBottom: 30,
  },
  button: {
    marginTop: "auto",
    backgroundColor: "#2E64FE",
    paddingVertical: 16,
    borderRadius: 10,
    alignItems: "center",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
});

export default ForgotPasswordScreen;
