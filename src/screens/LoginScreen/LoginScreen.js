import React, { useEffect, useState } from "react";
import {
  View,
  SafeAreaView,
  TouchableOpacity,
  StyleSheet,
  Image,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
} from "react-native";
import BackButton from "../../components/BackButton";
import InputField from "../../components/InputField";
import CustomCheckbox from "../../components/CustomCheckbox";
import SocialButton from "./components/SocialButton";
import MyText from "../../components/MyText";
import icons from "../../assets/icons";
import commonStyles from "../../assets/commonStyles";
import { useDispatch, useSelector } from "react-redux";
import {
  login,
  setToken,
  setUser,
  socialLogin,
} from "../../redux/features/authSlice";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { CommonActions, useNavigation } from "@react-navigation/native";
import { storage } from "../../utils/storage";
import {
  LoginManager,
  AccessToken,
  GraphRequest,
  GraphRequestManager,
} from "react-native-fbsdk-next";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { appleAuth } from "@invertase/react-native-apple-authentication";
import {
  getStoredAppleUserInfo,
  storeAppleUserInfo,
} from "../../utils/commonHelpers";
import { PrimaryButton } from "../../components/Button";
import { DrawerLayoutAndroid } from "react-native-gesture-handler";

const LoginScreen = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [secure, setSecure] = useState(true);
  const [rememberMe, setRememberMe] = useState(false);
  const [errors, setErrors] = useState({});

  const [loading, setLoading] = useState(false);
  // console.log("🚀 ~ LoginScreen ~ state:", state);

  useEffect(() => {
    const loadStoredCredentials = async () => {
      try {
        const savedEmail = await AsyncStorage.getItem("email");
        const savedPassword = await AsyncStorage.getItem("password");
        console.log("Saved email and password:", savedEmail, savedPassword);

        if (savedEmail && savedPassword) {
          setEmail(savedEmail);
          setPassword(savedPassword);
          setRememberMe(true);
        }
      } catch (e) {
        console.error("Failed to load saved credentials:", e);
      }
    };

    loadStoredCredentials();
  }, []);

  const dispatch = useDispatch();
  const navigation = useNavigation();

  const validate = () => {
    const newErrors = {};
    // simple email regex
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      newErrors.email = "Email is required";
    } else if (!emailPattern.test(email)) {
      newErrors.email = "Enter a valid email";
    }
    if (!password) {
      newErrors.password = "Password is required";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (validate()) {
      const payload = {
        email: email,
        password: password,
      };
      console.log("Login payload:", payload);

      try {
        setLoading(true);
        await GoogleSignin.signOut();
        const response = await dispatch(login(payload));
        console.log("Login response:", JSON.stringify(response));
        if (response?.payload?.success) {
          console.log("Login successful");

          if (rememberMe) {
            await AsyncStorage.setItem("email", email);
            await AsyncStorage.setItem("password", password);
          } else {
            await AsyncStorage.removeItem("email");
            await AsyncStorage.removeItem("password");
          }

          navigation.navigate("TwoFactorScreen", {
            showEmailVerification: false,
            showPhoneVerification: true,
            subheading: "An OTP has been sent on your number",
            showProgressLine: false,
            type: "login",
            userID: response?.payload?.data?.userId,
            email: email,
            rememberMe: rememberMe,
          });
        } else {
          console.log("Login failed");
          if (
            response?.payload?.data?.message.toLowerCase().includes("email")
          ) {
            setErrors({ ...errors, email: response?.payload?.data?.message });
          } else if (
            response?.payload?.data?.message.toLowerCase().includes("password")
          ) {
            setErrors({
              ...errors,
              password: response?.payload?.data?.message,
            });
          } else {
            setErrors({ ...errors, email: response?.payload?.data?.message });
          }
        }
      } catch (error) {
        console.error("Login error:", error);
        // Handle error
      } finally {
        setLoading(false);
      }
    } else {
      console.log("Validation failed");
      // Handle validation failure
    }
  };

  const handleGoogleSignin = async () => {
    await GoogleSignin.hasPlayServices();
    await GoogleSignin.signOut();
    const userInfo = await GoogleSignin.signIn();
    console.log("Google Sign-In User Info:", userInfo);

    const payload = {
      email: userInfo?.data?.user?.email || "",
      social_login_id: userInfo?.data?.user?.id || "",
    };

    try {
      const response = await dispatch(socialLogin(payload));
      console.log("Google Sign-In Response:", JSON.stringify(response));
      if (response?.payload?.success) {
        console.log("Google Sign-In successful");
        await dispatch(setUser(response?.payload?.data?.user));
        await dispatch(setToken(response?.payload?.data?.accessToken));

        // return;

        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "MainApp" }],
          })
        );
      } else {
        console.log("Google Sign-In failed");
        setErrors({ ...errors, server: response?.payload?.data?.message });
      }
    } catch (error) {
      console.error("Google Sign-In Error:", error);
    }
    console.log("Google Sign-In Response:", response);
  };
  // const handleFacebookSignin = () => {
  //   // Handle Facebook Sign-In
  //   console.log("Facebook Sign-In");
  // };
  const handleAppleSignin = async () => {
    let finalEmail = "";
    let userId = "";
    try {
      const response = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
      });

      const { user, email, fullName } = response;
      userId = user;
      if (!email || !fullName?.givenName) {
        // fallback to stored data
        const stored = await getStoredAppleUserInfo(user);
        if (stored) {
          finalEmail = stored.email;
          console.log("Loaded from Keychain:", stored);

          // to do
          const payload = {
            email: finalEmail,
            social_login_id: userId,
          };
          const response = await dispatch(socialLogin(payload));
          console.log("Apple Sign-In Response:", JSON.stringify(response));
          if (response?.payload?.success) {
            console.log("Apple Sign-In successful");
            await dispatch(setUser(response?.payload?.data?.user));
            await dispatch(setToken(response?.payload?.data?.accessToken));

            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [{ name: "MainApp" }],
              })
            );
          } else {
            console.log("Apple Sign-In failed");
            setErrors({ ...errors, server: response?.payload?.data?.message });
          }
        } else {
          console.warn("No stored data found for Apple ID");
        }
      } else {
        // store it for next time
        await storeAppleUserInfo(user, { email, fullName });
        console.log("Stored user data in Keychain");
        finalEmail = email;

        // to do
        const payload = {
          email: finalEmail,
          social_login_id: userId,
        };
        const response = await dispatch(socialLogin(payload));
        console.log("Apple Sign-In Response:", JSON.stringify(response));
        if (response?.payload?.success) {
          console.log("Apple Sign-In successful");
          await dispatch(setUser(response?.payload?.data?.user));
          await dispatch(setToken(response?.payload?.data?.accessToken));

          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [{ name: "MainApp" }],
            })
          );
        } else {
          console.log("Apple Sign-In failed");
          setErrors({ ...errors, server: response?.payload?.data?.message });
        }
      }
      // Proceed with login using user, finalEmail, finalFullName
    } catch (error) {
      console.error("Apple Sign-In Error:", error);
    }
  };

  async function handleFacebookSignin() {
    LoginManager.logOut(); // Ensures a fresh login
    try {
      // Login with permissions
      const loginResult = await LoginManager.logInWithPermissions([
        "public_profile",
        "email",
      ]);

      if (loginResult.isCancelled) {
        throw new Error("User cancelled the login process");
      }

      // Get the access token
      const data = await AccessToken.getCurrentAccessToken();

      if (!data || !data.accessToken) {
        throw new Error("Something went wrong obtaining access token");
      }

      // Graph API to fetch user details
      const graphRequest = new GraphRequest(
        "/me",
        {
          accessToken: data.accessToken,
          parameters: {
            fields: {
              string: "id, name, first_name, last_name, email, picture",
            },
          },
        },
        async (error, result) => {
          if (error) {
            console.log("Graph API Error:", error);
            Alert.alert("Error", "Unable to fetch Facebook user details");
          } else {
            console.log("Graph API Result:", result);

            // Map the data to your fields
            const socialData = {
              social_type: "facebook",
              social_id: result.id,
              first_name: result.first_name,
              last_name: result.last_name,
              email: result.email,
            };

            console.log("Social Data:", socialData);

            // API Call or further handling
            await dispatch(
              socialLoginUser({ socialData: socialData, navigation })
            );
          }
        }
      );

      // Make the Graph API request
      new GraphRequestManager().addRequest(graphRequest).start();
    } catch (error) {
      console.log("Facebook Login Error:", error.message);
      Alert.alert("Error", error.message);
    }
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={styles.container}>
        <ScrollView>
          <View style={styles.inner}>
            <BackButton />
            <MyText h3 bold style={styles.title}>
              Welcome back! Glad to see you, again!
            </MyText>

            <InputField
              label="Email*"
              placeholder="Enter Email"
              value={email}
              onChangeText={(text) => {
                setEmail(text);
                if (errors.email) setErrors({ ...errors, email: "" });
              }}
              error={errors.email}
            />

            <InputField
              label="Password*"
              placeholder="Enter password"
              secure={secure}
              value={password}
              onChangeText={(text) => {
                setPassword(text);
                if (errors.password) setErrors({ ...errors, password: "" });
              }}
              rightIcon={
                <TouchableOpacity onPress={() => setSecure(!secure)}>
                  <Image
                    source={secure ? icons.eyeClosed : icons.eye}
                    style={commonStyles.smallIcon}
                    resizeMode="contain"
                  />
                </TouchableOpacity>
              }
              error={errors.password}
            />

            <View style={styles.row}>
              <CustomCheckbox
                checked={rememberMe}
                onChange={() => setRememberMe(!rememberMe)}
              />
              <MyText p style={{ marginLeft: 6 }}>
                Remember me
              </MyText>
              <TouchableOpacity
                style={{ marginLeft: "auto" }}
                onPress={() => navigation.navigate("ForgotPassword")}
              >
                <MyText p medium style={styles.forgot}>
                  Forgot Password?
                </MyText>
              </TouchableOpacity>
            </View>

            {/* <TouchableOpacity
              disabled={loading}
              activeOpacity={0.8}
              onPress={handleLogin}
              style={styles.loginButton}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <MyText h6 semibold style={styles.loginText}>
                  Login
                </MyText>
              )}
            </TouchableOpacity> */}
            <PrimaryButton
              disabled={loading}
              title="Login"
              onPress={handleLogin}
              loading={loading}
            />

            <MyText p style={styles.orText}>
              -or sign up via-
            </MyText>

            <View style={styles.socialRow}>
              <SocialButton
                icon={icons.googleIcon}
                label="Google"
                onPress={handleGoogleSignin}
              />
              <SocialButton
                icon={icons.facebookIcon}
                label="Facebook"
                onPress={handleFacebookSignin}
              />
              {Platform.OS === "ios" && (
                <SocialButton
                  icon={icons.appleIcon}
                  label="Apple"
                  onPress={handleAppleSignin}
                />
              )}
            </View>
          </View>

          {/* server error messages */}
          {errors.server && (
            <View style={{ padding: 20 }}>
              <MyText p center style={{ color: "red" }}>
                {errors.server}
              </MyText>
            </View>
          )}

          {/* Footer */}

          <View style={styles.footer}>
            <MyText p>Don’t have an account? </MyText>
            <TouchableOpacity onPress={() => navigation.navigate("SignUp")}>
              <MyText p medium style={styles.link}>
                Sign Up
              </MyText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  inner: { padding: 15, flex: 1 },
  title: { marginVertical: 20 },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  forgot: {
    color: "red",
  },
  loginButton: {
    backgroundColor: "#2E64FE",
    paddingVertical: 14,
    borderRadius: 10,
    marginTop: 10,
  },
  loginText: {
    color: "#fff",
    textAlign: "center",
    fontSize: 16,
  },
  orText: {
    textAlign: "center",
    marginVertical: 20,
    color: "#888",
  },
  socialRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 4
  },
  footer: {
    marginTop: 30,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    paddingVertical: 20,
  },
  link: {
    color: "red",
    fontWeight: "500",
  },
});

export default LoginScreen;
