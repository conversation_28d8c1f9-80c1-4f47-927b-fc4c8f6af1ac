import {
  Image,
  StyleSheet,
  View,
  TouchableOpacity,
  Text,
  FlatList,
} from "react-native";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

import icons from "../../assets/icons";
import Header from "../../components/Header";
import colors from "../../assets/colors";
import { PrimaryButton } from "../../components/Button";
import ContactCard from "../../components/ContactCard";
import {
  getDuplicates,
  mergeAllContacts,
  mergeContacts,
} from "../../redux/features/contactSlice";
import AppLoader from "../../components/AppLoader";
import { showToast } from "../../utils/toastConfig";
import MyText from "../../components/MyText";
import commonStyles from "../../assets/commonStyles";

const DuplicateContactScreen = ({ navigation, route }) => {
  // const duplicatesState = useSelector((state) => state.contacts.duplicates);
  const duplicatesState = useSelector(
    (state) => state.contactSlice.getDuplicatesState
  );

  const { loading: isDuplicatesLoading, data: duplicateContactData } =
    duplicatesState || {};
  const duplicateContacts = duplicateContactData?.data?.duplicateGroups;
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(getDuplicates());
  }, []);

  const [isLoading, setIsLoading] = React.useState(false);

  const handleMerge = (contacts, refetch = false) => {
    console.log("🚀 ~ handleMerge ~ contacts:", contacts, "refetch:", refetch);
    const contactIds = contacts.map((contact) => contact.id);
    setIsLoading(true);
    dispatch(mergeContacts(contacts))
      .unwrap()
      .then(() => {
        showToast("success", "Contacts merged successfully");
        console.log("Contacts merged successfully");
        // navigation.goBack();
        dispatch(getDuplicates());
        // if (refetch && typeof route.params.onRefetch === "function") {
        //   route.params.onRefetch();
        // }
      })
      .catch((error) => {
        showToast("error", "Something went wrong. Please try again.");
        console.log("Error merging contacts:", error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };
  const handleMergeAllContacts = (contacts, refetch = false) => {
    setIsLoading(true);
    dispatch(mergeAllContacts(duplicateContacts))
      .unwrap()
      .then(() => {
        showToast("success", "Contacts merged successfully");
        console.log("Contacts merged successfully");
        navigation.goBack();
        // dispatch(getDuplicates());
        if (refetch && typeof route.params.onRefetch === "function") {
          route.params.onRefetch();
        }
      })
      .catch((error) => {
        showToast("error", "Something went wrong. Please try again.");
        console.log("Error merging contacts:", error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const renderDuplicateItem = ({ item, index }) => {
    return (
      <View style={styles.card}>
        {item.contacts.map((contact, contactIndex) => {
          console.log("🚀 ~ renderDuplicateItem ~ contact:", contact);
          return (
            <View key={contactIndex} style={styles.contactRow}>
              <ContactCard
                name={
                  `${contact?.firstName || ""} ${
                    contact?.middleName || contact?.name || ""
                  } ${
                    contact?.lastName || contact?.name || ""
                  }`.trim() || "Unknown"
                }
                phone={
                  contact?.phoneNumbers?.[0]?.number ||
                  contact?.emails?.[0]?.address ||
                  "N/A"
                }
                imgUrl={contact.profile_image}
              />
            </View>
          );
        })}

        <View style={styles.buttonRow}>
          <PrimaryButton
            title="Merge"
            style={{ width: "45%", height: 54 }}
            onPress={() => handleMerge(item.contacts, true)}
          />
          <PrimaryButton
            title={item.contacts?.length > 2 ? "Keep All" : "Keep Both"}
            style={{ width: "45%", height: 54, backgroundColor: "#E4E7EB" }}
            textStyle={{ color: "#000" }}
            onPress={() => navigation.goBack()}
          />
        </View>
      </View>
    );
  };

  return (
    <View style={{ flex: 1, backgroundColor: colors.white }}>
      <Header
        title="Duplicate Contacts"
        leftIcon={icons.backwBg}
        onPressLeft={() => navigation.goBack()}
        pb={20}
        textCenter
      />
      <AppLoader isLoading={isLoading || isDuplicatesLoading} />
      <FlatList
        data={duplicateContacts}
        renderItem={renderDuplicateItem}
        contentContainerStyle={styles.container}
        ListHeaderComponent={
          <View style={commonStyles.row}>
            {/* <PrimaryButton
              title={"Keep Both"}
              style={{ width: "25%", height: 24, backgroundColor: "#E4E7EB" }}
              textStyle={{ color: "#000" }}
              onPress={() => navigation.goBack()}
            /> */}
            <MyText
              color={colors.primary}
              underline
              onPress={handleMergeAllContacts}
            >
              Merge All Contacts
            </MyText>
            <MyText
              color={colors.black}
              underline
              onPress={() => navigation?.goBack()}
            >
              Keep All Contacts
            </MyText>
          </View>
        }
      />
    </View>
  );
};

export default DuplicateContactScreen;

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  card: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
    marginBottom: 20,
    backgroundColor: "#fff",
    borderRadius: 8,
  },
  contactRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  profileImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  contactTextBox: {
    flex: 1,
  },
  name: {
    fontWeight: "bold",
    fontSize: 16,
    marginBottom: 4,
    color: "#000",
  },
  value: {
    fontSize: 14,
    color: "#555",
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
});
